using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// 上下文信息
    /// </summary>
    public class ContextInformation
    {
        /// <summary>
        /// 会话上下文
        /// </summary>
        public SessionContextData SessionContext { get; set; } = new();

        /// <summary>
        /// 用户上下文
        /// </summary>
        public UserContextData UserContext { get; set; } = new();

        /// <summary>
        /// 系统上下文
        /// </summary>
        public SystemContextData SystemContext { get; set; } = new();

        /// <summary>
        /// 对话上下文
        /// </summary>
        public ConversationContextData ConversationContext { get; set; } = new();

        /// <summary>
        /// 时间上下文
        /// </summary>
        public TemporalContextData TemporalContext { get; set; } = new();

        /// <summary>
        /// 环境上下文
        /// </summary>
        public EnvironmentContextData EnvironmentContext { get; set; } = new();

        /// <summary>
        /// 自定义上下文
        /// </summary>
        public Dictionary<string, object> CustomContext { get; set; } = new();

        /// <summary>
        /// 上下文版本
        /// </summary>
        public int ContextVersion { get; set; } = 1;

        /// <summary>
        /// 上下文校验和
        /// </summary>
        public string ContextChecksum { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 是否只读
        /// </summary>
        public bool IsReadOnly { get; set; } = false;

        /// <summary>
        /// 压缩级别
        /// </summary>
        public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.None;

        /// <summary>
        /// 加密级别
        /// </summary>
        public string EncryptionLevel { get; set; } = "None";

        /// <summary>
        /// 任务输出缓存
        /// </summary>
        public Dictionary<Guid, object> TaskOutputs { get; set; } = new();
    }

    /// <summary>
    /// 会话上下文数据
    /// </summary>
    public class SessionContextData
    {
        public string SessionId { get; set; } = string.Empty;
        public DateTime SessionStartTime { get; set; } = DateTime.UtcNow;
        public TimeSpan SessionDuration => DateTime.UtcNow - SessionStartTime;
        public string ClientInfo { get; set; } = string.Empty;
        public string IpAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public Dictionary<string, object> SessionData { get; set; } = new();
    }

    /// <summary>
    /// 用户上下文数据
    /// </summary>
    public class UserContextData
    {
        public string UserId { get; set; } = string.Empty;
        public string UserName { get; set; } = string.Empty;
        public string PreferredLanguage { get; set; } = "en";
        public string TimeZone { get; set; } = "UTC";
        public Dictionary<string, object> UserPreferences { get; set; } = new();
        public Dictionary<string, object> UserProfile { get; set; } = new();
        public List<string> UserRoles { get; set; } = new();
        public List<string> UserPermissions { get; set; } = new();
    }

    /// <summary>
    /// 系统上下文数据
    /// </summary>
    public class SystemContextData
    {
        public string SystemVersion { get; set; } = "1.0.0";
        public string Environment { get; set; } = "Production";
        public Dictionary<string, object> SystemSettings { get; set; } = new();
        public Dictionary<string, object> FeatureFlags { get; set; } = new();
        public List<string> EnabledServices { get; set; } = new();
        public Dictionary<string, object> SystemMetrics { get; set; } = new();
    }

    /// <summary>
    /// 对话上下文数据
    /// </summary>
    public class ConversationContextData
    {
        public string ConversationTopic { get; set; } = string.Empty;
        public List<string> Keywords { get; set; } = new();
        public Dictionary<string, object> Entities { get; set; } = new();
        public string Intent { get; set; } = string.Empty;
        public double IntentConfidence { get; set; } = 0.0;
        public Dictionary<string, object> ConversationMemory { get; set; } = new();
        public List<string> ConversationHistory { get; set; } = new();
    }

    /// <summary>
    /// 时间上下文数据
    /// </summary>
    public class TemporalContextData
    {
        public DateTime CurrentTime { get; set; } = DateTime.UtcNow;
        public string TimeZone { get; set; } = "UTC";
        public DayOfWeek DayOfWeek { get; set; } = DateTime.UtcNow.DayOfWeek;
        public int HourOfDay { get; set; } = DateTime.UtcNow.Hour;
        public string Season { get; set; } = string.Empty;
        public Dictionary<string, object> TemporalEvents { get; set; } = new();
    }

    /// <summary>
    /// 环境上下文数据
    /// </summary>
    public class EnvironmentContextData
    {
        public string Platform { get; set; } = string.Empty;
        public string Device { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
        public string Network { get; set; } = string.Empty;
        public Dictionary<string, object> EnvironmentVariables { get; set; } = new();
        public Dictionary<string, object> DeviceCapabilities { get; set; } = new();
    }

    /// <summary>
    /// 对话状态
    /// </summary>
    public class ConversationState
    {
        public ConversationStatus CurrentStatus { get; set; } = ConversationStatus.Created;
        public int CurrentRound { get; set; } = 1;
        public bool IsWaitingForUser { get; set; } = false;
        public bool IsProcessing { get; set; } = false;
        public string CurrentPhase { get; set; } = "Initialization";
        public Dictionary<string, object> StateVariables { get; set; } = new();
        public DateTime LastStatusChange { get; set; } = DateTime.UtcNow;
        public List<StateTransition> StateHistory { get; set; } = new();
    }

    /// <summary>
    /// 状态转换记录
    /// </summary>
    public class StateTransition
    {
        public ConversationStatus FromState { get; set; }
        public ConversationStatus ToState { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string Reason { get; set; } = string.Empty;
        public string TriggeredBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 对话结果
    /// </summary>
    public class ConversationResult
    {
        public bool IsSuccess { get; set; } = false;
        public string FinalAnswer { get; set; } = string.Empty;
        public object? ResultData { get; set; }
        public double ConfidenceScore { get; set; } = 0.0;
        public List<string> KeyPoints { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan ProcessingTime { get; set; }
        public List<string> Sources { get; set; } = new();
        public ConversationQuality Quality { get; set; } = new();
    }

    /// <summary>
    /// 对话摘要
    /// </summary>
    public class ConversationSummary
    {
        public Guid ConversationId { get; set; }
        public string Title { get; set; } = string.Empty;
        public ConversationStatus Status { get; set; }
        public TimeSpan? Duration { get; set; }
        public int MessageCount { get; set; }
        public int TaskCount { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public string Summary { get; set; } = string.Empty;
        public List<string> Tags { get; set; } = new();
    }

    /// <summary>
    /// 对话轮次
    /// </summary>
    public class ConversationRound
    {
        /// <summary>
        /// 轮次编号
        /// </summary>
        public int RoundNumber { get; set; }

        /// <summary>
        /// 轮次开始时间
        /// </summary>
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 轮次结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 轮次中的任务
        /// </summary>
        public List<Guid> TaskIds { get; set; } = new();

        /// <summary>
        /// 轮次中的消息
        /// </summary>
        public List<Guid> MessageIds { get; set; } = new();

        /// <summary>
        /// 轮次状态
        /// </summary>
        public RoundStatus Status { get; set; } = RoundStatus.InProgress;

        /// <summary>
        /// 轮次结果
        /// </summary>
        public object? Result { get; set; }

        /// <summary>
        /// 轮次持续时间
        /// </summary>
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;

        /// <summary>
        /// 是否检测到循环
        /// </summary>
        public bool LoopDetected { get; set; } = false;

        /// <summary>
        /// 循环模式
        /// </summary>
        public string LoopPattern { get; set; } = string.Empty;
    }

    /// <summary>
    /// 轮次状态
    /// </summary>
    public enum RoundStatus
    {
        /// <summary>
        /// 进行中
        /// </summary>
        InProgress,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed,

        /// <summary>
        /// 已失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 循环中断
        /// </summary>
        LoopInterrupted
    }
}
