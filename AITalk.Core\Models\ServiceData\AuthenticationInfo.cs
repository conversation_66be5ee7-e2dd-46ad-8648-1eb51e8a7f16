using System;
using System.Collections.Generic;
using System.Security.Cryptography.X509Certificates;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 认证信息
    /// </summary>
    public class AuthenticationInfo
    {
        /// <summary>
        /// 认证类型
        /// </summary>
        public AuthenticationType AuthType { get; set; } = AuthenticationType.ApiKey;

        /// <summary>
        /// API密钥（加密存储）
        /// </summary>
        public string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// 密钥（加密存储）
        /// </summary>
        public string SecretKey { get; set; } = string.Empty;

        /// <summary>
        /// 访问令牌
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 刷新令牌
        /// </summary>
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// 令牌过期时间
        /// </summary>
        public DateTime? TokenExpiry { get; set; }

        /// <summary>
        /// 组织ID
        /// </summary>
        public string OrganizationId { get; set; } = string.Empty;

        /// <summary>
        /// 项目ID
        /// </summary>
        public string ProjectId { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 权限范围
        /// </summary>
        public List<string> Scopes { get; set; } = new();

        /// <summary>
        /// 自定义请求头
        /// </summary>
        public Dictionary<string, string> CustomHeaders { get; set; } = new();

        /// <summary>
        /// 证书路径
        /// </summary>
        public string CertificatePath { get; set; } = string.Empty;

        /// <summary>
        /// 加密密钥
        /// </summary>
        public string EncryptionKey { get; set; } = string.Empty;

        /// <summary>
        /// 用户名（用于Basic认证）
        /// </summary>
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// 密码（用于Basic认证）
        /// </summary>
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// OAuth2客户端ID
        /// </summary>
        public string ClientId { get; set; } = string.Empty;

        /// <summary>
        /// OAuth2客户端密钥
        /// </summary>
        public string ClientSecret { get; set; } = string.Empty;

        /// <summary>
        /// OAuth2授权URL
        /// </summary>
        public string AuthorizationUrl { get; set; } = string.Empty;

        /// <summary>
        /// OAuth2令牌URL
        /// </summary>
        public string TokenUrl { get; set; } = string.Empty;

        /// <summary>
        /// OAuth2重定向URI
        /// </summary>
        public string RedirectUri { get; set; } = string.Empty;

        /// <summary>
        /// JWT签名密钥
        /// </summary>
        public string JwtSigningKey { get; set; } = string.Empty;

        /// <summary>
        /// JWT发行者
        /// </summary>
        public string JwtIssuer { get; set; } = string.Empty;

        /// <summary>
        /// JWT受众
        /// </summary>
        public string JwtAudience { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用双因素认证
        /// </summary>
        public bool TwoFactorEnabled { get; set; } = false;

        /// <summary>
        /// 双因素认证密钥
        /// </summary>
        public string TwoFactorSecret { get; set; } = string.Empty;

        /// <summary>
        /// 认证创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 认证更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后验证时间
        /// </summary>
        public DateTime? LastValidatedAt { get; set; }

        /// <summary>
        /// 认证是否有效
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// 认证失败次数
        /// </summary>
        public int FailureCount { get; set; } = 0;

        /// <summary>
        /// 最大失败次数
        /// </summary>
        public int MaxFailures { get; set; } = 5;

        /// <summary>
        /// 锁定到期时间
        /// </summary>
        public DateTime? LockoutExpiry { get; set; }
    }
}
