using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AITalk.Core.Enums;
using AITalk.Core.Models.ConversationData;

namespace AITalk.Core.Models
{
    /// <summary>
    /// AI对话数据 - 存储AITalk类的所有数据，支持自循环和多轮调度
    /// </summary>
    public class AITalkData
    {
        /// <summary>
        /// 对话唯一标识
        /// </summary>
        public Guid ConversationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 对话名称
        /// </summary>
        public string ConversationName { get; set; } = string.Empty;

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 对话类型
        /// </summary>
        public ConversationType ConversationType { get; set; } = ConversationType.MultiRound;

        /// <summary>
        /// 对话状态
        /// </summary>
        public ConversationStatus Status { get; set; } = ConversationStatus.Created;

        // === 多轮任务调度核心数据 ===
        /// <summary>
        /// 任务队列 - AITalk的核心，支持多轮调度
        /// </summary>
        public List<AITask> TaskQueue { get; set; } = new();

        /// <summary>
        /// 任务注册表 - 快速查找任务
        /// </summary>
        public Dictionary<Guid, AITask> TaskRegistry { get; set; } = new();

        /// <summary>
        /// 任务依赖关系图
        /// </summary>
        public Dictionary<Guid, List<Guid>> TaskDependencies { get; set; } = new();

        /// <summary>
        /// 执行队列 - 当前正在执行的任务
        /// </summary>
        public Queue<AITask> ExecutionQueue { get; set; } = new();

        /// <summary>
        /// 已完成的任务
        /// </summary>
        public List<AITask> CompletedTasks { get; set; } = new();

        /// <summary>
        /// 失败的任务
        /// </summary>
        public List<AITask> FailedTasks { get; set; } = new();

        /// <summary>
        /// 当前执行的任务
        /// </summary>
        public AITask? CurrentExecutingTask { get; set; }

        /// <summary>
        /// 最大并发任务数
        /// </summary>
        public int MaxConcurrentTasks { get; set; } = 3;

        /// <summary>
        /// 当前并发任务数
        /// </summary>
        public int CurrentConcurrentTasks { get; set; } = 0;

        // === 自循环检测和控制数据 ===
        /// <summary>
        /// 循环检测映射 - 用于检测重复的任务模式
        /// </summary>
        public Dictionary<string, int> LoopDetectionMap { get; set; } = new();

        /// <summary>
        /// 对话轮次历史
        /// </summary>
        public List<ConversationRound> ConversationRounds { get; set; } = new();

        /// <summary>
        /// 最大循环迭代次数
        /// </summary>
        public int MaxLoopIterations { get; set; } = 10;

        /// <summary>
        /// 当前循环计数
        /// </summary>
        public int CurrentLoopCount { get; set; } = 0;

        /// <summary>
        /// 是否处于循环状态
        /// </summary>
        public bool IsInLoop { get; set; } = false;

        /// <summary>
        /// 循环检测结果
        /// </summary>
        public LoopDetectionResult LoopDetectionResult { get; set; } = LoopDetectionResult.NoLoop;

        /// <summary>
        /// 循环中断策略
        /// </summary>
        public LoopBreakingStrategy LoopBreakingStrategy { get; set; } = LoopBreakingStrategy.ModifyTaskParameters;

        // === 对话内容数据 ===
        /// <summary>
        /// 消息历史
        /// </summary>
        public List<ConversationMessage> Messages { get; set; } = new();

        /// <summary>
        /// 对话上下文
        /// </summary>
        public Dictionary<string, object> ConversationContext { get; set; } = new();

        /// <summary>
        /// 任务输出缓存
        /// </summary>
        public Dictionary<Guid, object> TaskOutputs { get; set; } = new();

        /// <summary>
        /// 中间结果存储
        /// </summary>
        public Dictionary<string, object> IntermediateResults { get; set; } = new();

        /// <summary>
        /// 最终结果
        /// </summary>
        public object? FinalResult { get; set; }

        // === 配置和限制 ===
        /// <summary>
        /// 最大轮次
        /// </summary>
        public int MaxRounds { get; set; } = 50;

        /// <summary>
        /// 当前轮次
        /// </summary>
        public int CurrentRound { get; set; } = 1;

        /// <summary>
        /// 最大消息数
        /// </summary>
        public int MaxMessages { get; set; } = 1000;

        /// <summary>
        /// 超时时间
        /// </summary>
        public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(30);

        /// <summary>
        /// 是否启用自动保存
        /// </summary>
        public bool EnableAutoSave { get; set; } = true;

        /// <summary>
        /// 自动保存间隔
        /// </summary>
        public TimeSpan AutoSaveInterval { get; set; } = TimeSpan.FromMinutes(5);

        // === 时间戳和元数据 ===
        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; } = new();

        // === AI流式传输和回调功能 ===

        /// <summary>
        /// AI内容回调函数委托
        /// </summary>
        /// <param name="newContent">新接收到的内容片段</param>
        /// <param name="totalContent">累积的全部内容</param>
        /// <param name="data">AITalkData实例</param>
        public delegate void AIContentCallback(string newContent, string totalContent, AITalkData data);

        /// <summary>
        /// AI流式传输内容缓存 - 使用StringBuilder实现动态内容累积
        /// </summary>
        public StringBuilder AIStreamContent { get; private set; } = new StringBuilder();

        /// <summary>
        /// AI最终响应内容
        /// </summary>
        public string AIResponse { get; set; } = string.Empty;

        /// <summary>
        /// 内容回调函数列表
        /// </summary>
        private readonly List<AIContentCallback> _contentCallbacks = new List<AIContentCallback>();

        /// <summary>
        /// 回调函数访问锁，确保线程安全
        /// </summary>
        private readonly object _callbackLock = new object();

        /// <summary>
        /// 添加消息
        /// </summary>
        public void AddMessage(ConversationMessage message)
        {
            Messages.Add(message);
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 添加任务
        /// </summary>
        public void AddTask(AITask task)
        {
            TaskQueue.Add(task);
            TaskRegistry[task.TaskId] = task;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取下一个待执行任务
        /// </summary>
        public AITask? GetNextTask()
        {
            var pendingTasks = TaskQueue.Where(t => t.Status == Enums.TaskStatus.Pending).ToList();
            return pendingTasks.FirstOrDefault();
        }

        /// <summary>
        /// 开始执行任务
        /// </summary>
        public void StartTask(AITask task)
        {
            CurrentExecutingTask = task;
            CurrentConcurrentTasks++;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 完成任务
        /// </summary>
        public void CompleteTask(AITask task, object? output = null)
        {
            task.Complete(output);
            CompletedTasks.Add(task);

            // 更新任务输出缓存
            if (output != null)
            {
                TaskOutputs[task.TaskId] = output;
            }

            if (CurrentExecutingTask?.TaskId == task.TaskId)
            {
                CurrentExecutingTask = null;
                CurrentConcurrentTasks = Math.Max(0, CurrentConcurrentTasks - 1);
            }

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 任务失败
        /// </summary>
        public void FailTask(AITask task, Exception error)
        {
            task.Fail(error);
            FailedTasks.Add(task);

            if (CurrentExecutingTask?.TaskId == task.TaskId)
            {
                CurrentExecutingTask = null;
                CurrentConcurrentTasks = Math.Max(0, CurrentConcurrentTasks - 1);
            }

            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新对话状态
        /// </summary>
        public void UpdateStatus(ConversationStatus status, string reason = "")
        {
            Status = status;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取当前轮次
        /// </summary>
        public int GetCurrentRound()
        {
            var allTasks = new List<AITask>();
            allTasks.AddRange(CompletedTasks);
            allTasks.AddRange(FailedTasks);
            allTasks.AddRange(TaskQueue.Where(t => t.Status != Enums.TaskStatus.Pending));

            return allTasks.Count > 0 ? allTasks.Max(t => t.Round) : CurrentRound;
        }

        /// <summary>
        /// 检查对话是否完成
        /// </summary>
        public bool IsCompleted()
        {
            return Status == ConversationStatus.Completed ||
                   Status == ConversationStatus.Failed ||
                   Status == ConversationStatus.Cancelled;
        }

        /// <summary>
        /// 检查对话是否活跃
        /// </summary>
        public bool IsActive()
        {
            return !IsCompleted() &&
                   (TaskQueue.Any(t => t.Status == Enums.TaskStatus.Pending) || CurrentExecutingTask != null);
        }

        // === AI内容流式传输和回调管理方法 ===

        /// <summary>
        /// 注册AI内容回调函数
        /// 新注册的回调函数会立即接收到已有的内容
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void RegisterContentCallback(AIContentCallback callback)
        {
            lock (_callbackLock)
            {
                _contentCallbacks.Add(callback);

                // 如果已有内容，立即回调给新注册的函数
                var currentContent = AIStreamContent.ToString();
                if (!string.IsNullOrEmpty(currentContent))
                {
                    try
                    {
                        callback(currentContent, currentContent, this);
                    }
                    catch (Exception ex)
                    {
                        // 记录回调异常，但不影响其他回调
                        Console.WriteLine($"回调函数执行异常: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 取消注册AI内容回调函数
        /// </summary>
        /// <param name="callback">要取消的回调函数</param>
        public void UnregisterContentCallback(AIContentCallback callback)
        {
            lock (_callbackLock)
            {
                _contentCallbacks.Remove(callback);
            }
        }

        /// <summary>
        /// 清除所有AI内容回调函数
        /// </summary>
        public void ClearContentCallbacks()
        {
            lock (_callbackLock)
            {
                _contentCallbacks.Clear();
            }
        }

        /// <summary>
        /// 获取当前注册的回调函数数量
        /// </summary>
        /// <returns>回调函数数量</returns>
        public int GetCallbackCount()
        {
            lock (_callbackLock)
            {
                return _contentCallbacks.Count;
            }
        }

        /// <summary>
        /// 添加AI流式传输内容 - 核心方法
        /// 这是流式传输的核心方法，每次接收到新内容时调用
        /// </summary>
        /// <param name="newContent">新接收到的内容片段</param>
        public void AppendAIStreamContent(string newContent)
        {
            if (string.IsNullOrEmpty(newContent)) return;

            string fullContent;
            List<AIContentCallback> callbacksCopy;

            lock (_callbackLock)
            {
                // 添加到StringBuilder
                AIStreamContent.Append(newContent);
                fullContent = AIStreamContent.ToString();

                // 复制回调列表，避免在锁外执行时被修改
                callbacksCopy = new List<AIContentCallback>(_contentCallbacks);
            }

            // 在锁外执行回调，避免死锁
            foreach (var callback in callbacksCopy)
            {
                try
                {
                    callback(newContent, fullContent, this);
                }
                catch (Exception ex)
                {
                    // 记录回调异常，但不影响其他回调
                    Console.WriteLine($"回调函数执行异常: {ex.Message}");
                }
            }

            // 更新最后修改时间
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 完成AI内容接收，将StringBuilder内容设置为最终AIResponse
        /// </summary>
        public void FinalizeAIContent()
        {
            lock (_callbackLock)
            {
                AIResponse = AIStreamContent.ToString();
            }
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 清空AI流式内容缓存
        /// </summary>
        public void ClearAIStreamContent()
        {
            lock (_callbackLock)
            {
                AIStreamContent.Clear();
            }
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// 获取当前AI流式内容
        /// </summary>
        /// <returns>当前累积的流式内容</returns>
        public string GetCurrentStreamContent()
        {
            lock (_callbackLock)
            {
                return AIStreamContent.ToString();
            }
        }

        /// <summary>
        /// 获取AI流式内容的长度
        /// </summary>
        /// <returns>内容长度</returns>
        public int GetStreamContentLength()
        {
            lock (_callbackLock)
            {
                return AIStreamContent.Length;
            }
        }

        /// <summary>
        /// 检查是否有流式内容
        /// </summary>
        /// <returns>是否有内容</returns>
        public bool HasStreamContent()
        {
            lock (_callbackLock)
            {
                return AIStreamContent.Length > 0;
            }
        }
    }
}
