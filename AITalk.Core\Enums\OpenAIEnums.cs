using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// OpenAI兼容提供商类型枚举
    /// 定义支持的各种AI服务提供商类型，包括云服务和本地部署
    /// </summary>
    public enum ProviderType
    {
        /// <summary>
        /// OpenAI - 官方OpenAI服务
        /// </summary>
        OpenAI,

        /// <summary>
        /// Azure - 微软Azure OpenAI服务
        /// </summary>
        Azure,

        /// <summary>
        /// Anthropic - Anthropic Claude服务
        /// </summary>
        Anthropic,

        /// <summary>
        /// Google - Google AI服务（如Gemini）
        /// </summary>
        Google,

        /// <summary>
        /// Cohere - Cohere AI服务
        /// </summary>
        Cohere,

        /// <summary>
        /// HuggingFace - HuggingFace推理API
        /// </summary>
        HuggingFace,

        /// <summary>
        /// 本地LLM - 本地部署的大语言模型
        /// </summary>
        LocalLLM,

        /// <summary>
        /// 自定义提供商 - 用户自定义的服务提供商
        /// </summary>
        CustomProvider,

        /// <summary>
        /// Ollama - 本地Ollama服务
        /// </summary>
        Ollama,

        /// <summary>
        /// LM Studio - LM Studio本地服务
        /// </summary>
        LMStudio,

        /// <summary>
        /// Text Generation WebUI - 文本生成Web界面
        /// </summary>
        TextGenWebUI,

        /// <summary>
        /// FastChat - FastChat服务
        /// </summary>
        FastChat,

        /// <summary>
        /// vLLM - vLLM推理引擎
        /// </summary>
        vLLM,

        /// <summary>
        /// TGI - Text Generation Inference
        /// </summary>
        TGI
    }

    /// <summary>
    /// 兼容性级别枚举
    /// 定义不同提供商与OpenAI API的兼容程度
    /// </summary>
    public enum CompatibilityLevel
    {
        /// <summary>
        /// 完全兼容 - 100%兼容OpenAI API
        /// </summary>
        Full,

        /// <summary>
        /// 高度兼容 - 90%以上兼容，少数功能可能不支持
        /// </summary>
        High,

        /// <summary>
        /// 中等兼容 - 70-90%兼容，部分功能需要适配
        /// </summary>
        Medium,

        /// <summary>
        /// 低度兼容 - 50-70%兼容，需要大量适配工作
        /// </summary>
        Low,

        /// <summary>
        /// 最小兼容 - 仅支持基本功能
        /// </summary>
        Minimal,

        /// <summary>
        /// 不兼容 - 不支持OpenAI API格式
        /// </summary>
        None
    }

    /// <summary>
    /// 认证方法枚举
    /// 定义不同的API认证方式
    /// </summary>
    public enum AuthMethod
    {
        /// <summary>
        /// API密钥 - 使用API Key进行认证
        /// </summary>
        ApiKey,

        /// <summary>
        /// Bearer令牌 - 使用Bearer Token认证
        /// </summary>
        BearerToken,

        /// <summary>
        /// OAuth2 - 使用OAuth2协议认证
        /// </summary>
        OAuth2,

        /// <summary>
        /// 基础认证 - 使用用户名密码认证
        /// </summary>
        Basic,

        /// <summary>
        /// 自定义认证 - 自定义认证方式
        /// </summary>
        Custom,

        /// <summary>
        /// 无认证 - 不需要认证（如本地服务）
        /// </summary>
        None
    }

    /// <summary>
    /// 模型类型枚举
    /// 定义AI模型支持的不同功能类型
    /// </summary>
    public enum ModelType
    {
        /// <summary>
        /// 聊天完成 - 对话式AI模型
        /// </summary>
        ChatCompletion,

        /// <summary>
        /// 文本完成 - 文本续写模型
        /// </summary>
        TextCompletion,

        /// <summary>
        /// 嵌入向量 - 文本向量化模型
        /// </summary>
        Embedding,

        /// <summary>
        /// 图像生成 - 图像生成模型
        /// </summary>
        ImageGeneration,

        /// <summary>
        /// 音频转录 - 语音转文字模型
        /// </summary>
        AudioTranscription,

        /// <summary>
        /// 音频翻译 - 语音翻译模型
        /// </summary>
        AudioTranslation,

        /// <summary>
        /// 内容审核 - 内容安全检测模型
        /// </summary>
        Moderation,

        /// <summary>
        /// 微调模型 - 经过微调的自定义模型
        /// </summary>
        FineTuned,

        /// <summary>
        /// 自定义模型 - 用户自定义的模型类型
        /// </summary>
        Custom
    }

    /// <summary>
    /// 标准错误代码枚举
    /// 定义API调用可能遇到的各种错误类型
    /// </summary>
    public enum StandardErrorCode
    {
        /// <summary>
        /// 无效请求 - 请求格式或参数错误
        /// </summary>
        InvalidRequest,

        /// <summary>
        /// 认证失败 - API密钥无效或过期
        /// </summary>
        AuthenticationFailed,

        /// <summary>
        /// 权限拒绝 - 没有访问权限
        /// </summary>
        PermissionDenied,

        /// <summary>
        /// 未找到 - 请求的资源不存在
        /// </summary>
        NotFound,

        /// <summary>
        /// 速率限制超出 - 请求频率过高
        /// </summary>
        RateLimitExceeded,

        /// <summary>
        /// 配额超出 - 使用量超出配额限制
        /// </summary>
        QuotaExceeded,

        /// <summary>
        /// 服务器错误 - 服务器内部错误
        /// </summary>
        ServerError,

        /// <summary>
        /// 服务不可用 - 服务暂时不可用
        /// </summary>
        ServiceUnavailable,

        /// <summary>
        /// 超时 - 请求处理超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 无效模型 - 指定的模型不存在或不可用
        /// </summary>
        InvalidModel,

        /// <summary>
        /// 内容被过滤 - 内容违反安全策略
        /// </summary>
        ContentFiltered,

        /// <summary>
        /// Token限制超出 - 输入或输出Token数量超限
        /// </summary>
        TokenLimitExceeded,

        /// <summary>
        /// 配额不足 - 剩余配额不足
        /// </summary>
        InsufficientQuota,

        /// <summary>
        /// 模型过载 - 模型负载过高
        /// </summary>
        ModelOverloaded,

        /// <summary>
        /// 网关错误 - 网关或代理错误
        /// </summary>
        BadGateway,

        /// <summary>
        /// 未知错误 - 未分类的错误
        /// </summary>
        Unknown
    }

    /// <summary>
    /// 重试策略枚举
    /// 定义请求失败时的重试策略
    /// </summary>
    public enum RetryStrategyType
    {
        /// <summary>
        /// 不重试 - 失败后不进行重试
        /// </summary>
        None,

        /// <summary>
        /// 立即重试 - 失败后立即重试
        /// </summary>
        Immediate,

        /// <summary>
        /// 固定间隔 - 固定时间间隔重试
        /// </summary>
        Fixed,

        /// <summary>
        /// 线性递增 - 重试间隔线性递增
        /// </summary>
        Linear,

        /// <summary>
        /// 指数退避 - 重试间隔指数递增
        /// </summary>
        Exponential,

        /// <summary>
        /// 自定义策略 - 用户自定义重试策略
        /// </summary>
        Custom
    }

    /// <summary>
    /// 速率限制动作枚举
    /// 定义遇到速率限制时的处理动作
    /// </summary>
    public enum RateLimitAction
    {
        /// <summary>
        /// 等待 - 等待限制解除后重试
        /// </summary>
        Wait,

        /// <summary>
        /// 切换提供商 - 切换到其他服务提供商
        /// </summary>
        SwitchProvider,

        /// <summary>
        /// 加入队列 - 将请求加入等待队列
        /// </summary>
        Queue,

        /// <summary>
        /// 拒绝请求 - 直接拒绝当前请求
        /// </summary>
        Reject,

        /// <summary>
        /// 重试 - 稍后重试请求
        /// </summary>
        Retry
    }

    /// <summary>
    /// 认证恢复动作枚举
    /// 定义认证失败时的恢复策略
    /// </summary>
    public enum AuthRecoveryAction
    {
        /// <summary>
        /// 刷新令牌 - 尝试刷新访问令牌
        /// </summary>
        RefreshToken,

        /// <summary>
        /// 重新认证 - 重新进行认证流程
        /// </summary>
        ReAuthenticate,

        /// <summary>
        /// 切换凭据 - 切换到备用认证凭据
        /// </summary>
        SwitchCredentials,

        /// <summary>
        /// 手动处理 - 需要手动干预处理
        /// </summary>
        Manual,

        /// <summary>
        /// 失败 - 认证恢复失败
        /// </summary>
        Fail
    }

    /// <summary>
    /// 配额恢复动作枚举
    /// 定义配额耗尽时的恢复策略
    /// </summary>
    public enum QuotaRecoveryAction
    {
        /// <summary>
        /// 等待 - 等待配额重置
        /// </summary>
        Wait,

        /// <summary>
        /// 切换提供商 - 切换到其他服务提供商
        /// </summary>
        SwitchProvider,

        /// <summary>
        /// 升级配额 - 升级到更高配额计划
        /// </summary>
        UpgradeQuota,

        /// <summary>
        /// 使用备用 - 使用备用配额或账户
        /// </summary>
        UseBackup,

        /// <summary>
        /// 失败 - 配额恢复失败
        /// </summary>
        Fail
    }

    /// <summary>
    /// 本地LLM类型枚举
    /// 定义支持的本地大语言模型部署方式
    /// </summary>
    public enum LocalLLMType
    {
        /// <summary>
        /// Ollama - 本地Ollama服务
        /// </summary>
        Ollama,

        /// <summary>
        /// LM Studio - LM Studio本地服务
        /// </summary>
        LMStudio,

        /// <summary>
        /// Text Generation WebUI - 文本生成Web界面
        /// </summary>
        TextGenWebUI,

        /// <summary>
        /// FastChat - FastChat服务
        /// </summary>
        FastChat,

        /// <summary>
        /// vLLM - vLLM推理引擎
        /// </summary>
        vLLM,

        /// <summary>
        /// TGI - Text Generation Inference
        /// </summary>
        TGI,

        /// <summary>
        /// Kobold - KoboldAI服务
        /// </summary>
        Kobold,

        /// <summary>
        /// GPT4All - GPT4All本地模型
        /// </summary>
        GPT4All,

        /// <summary>
        /// Llama.cpp - Llama.cpp推理引擎
        /// </summary>
        LlamaCpp,

        /// <summary>
        /// 自定义 - 用户自定义的本地LLM
        /// </summary>
        Custom
    }

    /// <summary>
    /// 提供商健康状态枚举
    /// 定义服务提供商的健康状态
    /// </summary>
    public enum ProviderHealthStatus
    {
        /// <summary>
        /// 健康 - 服务正常运行
        /// </summary>
        Healthy,

        /// <summary>
        /// 降级 - 服务性能下降但仍可用
        /// </summary>
        Degraded,

        /// <summary>
        /// 不健康 - 服务不可用或严重故障
        /// </summary>
        Unhealthy,

        /// <summary>
        /// 未知 - 无法确定服务状态
        /// </summary>
        Unknown,

        /// <summary>
        /// 维护中 - 服务正在维护
        /// </summary>
        Maintenance
    }

    /// <summary>
    /// 流式响应状态枚举
    /// 定义流式传输的各种状态
    /// </summary>
    public enum StreamingStatus
    {
        /// <summary>
        /// 启动中 - 流式传输正在启动
        /// </summary>
        Starting,

        /// <summary>
        /// 传输中 - 正在进行流式传输
        /// </summary>
        Streaming,

        /// <summary>
        /// 已完成 - 流式传输完成
        /// </summary>
        Completed,

        /// <summary>
        /// 错误 - 流式传输出现错误
        /// </summary>
        Error,

        /// <summary>
        /// 已取消 - 流式传输被取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 超时 - 流式传输超时
        /// </summary>
        Timeout
    }

    /// <summary>
    /// 端点类型枚举
    /// 定义API支持的不同端点类型
    /// </summary>
    public enum EndpointType
    {
        /// <summary>
        /// 聊天完成 - 对话式聊天端点
        /// </summary>
        ChatCompletion,

        /// <summary>
        /// 文本完成 - 文本续写端点
        /// </summary>
        Completion,

        /// <summary>
        /// 嵌入向量 - 文本向量化端点
        /// </summary>
        Embedding,

        /// <summary>
        /// 模型列表 - 获取可用模型列表
        /// </summary>
        Models,

        /// <summary>
        /// 图像生成 - 图像生成端点
        /// </summary>
        ImageGeneration,

        /// <summary>
        /// 音频转录 - 语音转文字端点
        /// </summary>
        AudioTranscription,

        /// <summary>
        /// 音频翻译 - 语音翻译端点
        /// </summary>
        AudioTranslation,

        /// <summary>
        /// 内容审核 - 内容安全检测端点
        /// </summary>
        Moderation,

        /// <summary>
        /// 微调 - 模型微调端点
        /// </summary>
        FineTuning,

        /// <summary>
        /// 自定义 - 用户自定义端点
        /// </summary>
        Custom
    }

    /// <summary>
    /// 导出格式枚举
    /// 定义数据导出支持的格式
    /// </summary>
    public enum ExportFormat
    {
        /// <summary>
        /// JSON - JSON格式
        /// </summary>
        Json,

        /// <summary>
        /// CSV - 逗号分隔值格式
        /// </summary>
        Csv,

        /// <summary>
        /// Excel - Excel电子表格格式
        /// </summary>
        Excel,

        /// <summary>
        /// XML - XML格式
        /// </summary>
        Xml,

        /// <summary>
        /// YAML - YAML格式
        /// </summary>
        Yaml,

        /// <summary>
        /// 纯文本 - 纯文本格式
        /// </summary>
        Text
    }

    /// <summary>
    /// 缓存策略枚举
    /// 定义不同的缓存存储策略
    /// </summary>
    public enum CacheStrategy
    {
        /// <summary>
        /// 无缓存 - 不使用缓存
        /// </summary>
        None,

        /// <summary>
        /// 内存缓存 - 使用内存缓存
        /// </summary>
        Memory,

        /// <summary>
        /// 磁盘缓存 - 使用磁盘缓存
        /// </summary>
        Disk,

        /// <summary>
        /// Redis缓存 - 使用Redis缓存
        /// </summary>
        Redis,

        /// <summary>
        /// 数据库缓存 - 使用数据库缓存
        /// </summary>
        Database,

        /// <summary>
        /// 混合缓存 - 使用多种缓存策略组合
        /// </summary>
        Hybrid
    }




}
