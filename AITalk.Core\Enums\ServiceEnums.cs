using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// 服务器类型枚举
    /// 定义支持的AI服务提供商类型
    /// </summary>
    public enum ServiceType
    {
        /// <summary>
        /// OpenAI - OpenAI官方服务
        /// </summary>
        OpenAI,

        /// <summary>
        /// Claude - Anthropic Claude服务
        /// </summary>
        <PERSON>,

        /// <summary>
        /// Gemini - Google Gemini服务
        /// </summary>
        Gemini,

        /// <summary>
        /// 本地模型 - 本地部署的AI模型
        /// </summary>
        LocalModel,

        /// <summary>
        /// Azure - 微软Azure AI服务
        /// </summary>
        Azure,

        /// <summary>
        /// Cohere - Cohere AI服务
        /// </summary>
        Cohere,

        /// <summary>
        /// HuggingFace - HuggingFace推理服务
        /// </summary>
        HuggingFace,

        /// <summary>
        /// 自定义 - 用户自定义的服务类型
        /// </summary>
        Custom
    }

    /// <summary>
    /// 认证类型枚举
    /// 定义API访问的认证方式
    /// </summary>
    public enum AuthenticationType
    {
        /// <summary>
        /// API密钥 - 使用API Key进行认证
        /// </summary>
        ApiKey,

        /// <summary>
        /// OAuth2 - 使用OAuth2协议认证
        /// </summary>
        OAuth2,

        /// <summary>
        /// JWT - 使用JSON Web Token认证
        /// </summary>
        JWT,

        /// <summary>
        /// 基础认证 - 使用用户名密码认证
        /// </summary>
        Basic,

        /// <summary>
        /// Bearer令牌 - 使用Bearer Token认证
        /// </summary>
        BearerToken,

        /// <summary>
        /// 自定义 - 用户自定义的认证方式
        /// </summary>
        Custom
    }

    /// <summary>
    /// 协议类型枚举
    /// 定义网络通信协议类型
    /// </summary>
    public enum ProtocolType
    {
        /// <summary>
        /// HTTP - 超文本传输协议
        /// </summary>
        HTTP,

        /// <summary>
        /// HTTPS - 安全超文本传输协议
        /// </summary>
        HTTPS,

        /// <summary>
        /// WebSocket - WebSocket协议，支持双向通信
        /// </summary>
        WebSocket,

        /// <summary>
        /// gRPC - 高性能RPC框架
        /// </summary>
        gRPC
    }

    /// <summary>
    /// 服务状态枚举
    /// 定义AI服务的运行状态
    /// </summary>
    public enum ServiceStatus
    {
        /// <summary>
        /// 未知 - 服务状态未知
        /// </summary>
        Unknown,

        /// <summary>
        /// 在线 - 服务正常运行
        /// </summary>
        Online,

        /// <summary>
        /// 离线 - 服务不可用
        /// </summary>
        Offline,

        /// <summary>
        /// 维护中 - 服务正在维护
        /// </summary>
        Maintenance,

        /// <summary>
        /// 错误 - 服务出现错误
        /// </summary>
        Error,

        /// <summary>
        /// 降级 - 服务性能下降
        /// </summary>
        Degraded
    }

    /// <summary>
    /// 负载均衡策略枚举
    /// 定义多个服务实例间的负载分配策略
    /// </summary>
    public enum LoadBalancingStrategy
    {
        /// <summary>
        /// 轮询 - 按顺序轮流分配请求
        /// </summary>
        RoundRobin,

        /// <summary>
        /// 加权轮询 - 根据权重分配请求
        /// </summary>
        WeightedRoundRobin,

        /// <summary>
        /// 最少连接 - 分配给连接数最少的服务
        /// </summary>
        LeastConnections,

        /// <summary>
        /// 响应时间优先 - 分配给响应时间最短的服务
        /// </summary>
        ResponseTimeBased,

        /// <summary>
        /// 健康状态优先 - 优先分配给健康的服务
        /// </summary>
        HealthBased,

        /// <summary>
        /// 地理位置就近 - 根据地理位置就近分配
        /// </summary>
        GeographicProximity,

        /// <summary>
        /// 成本优化 - 优先使用成本较低的服务
        /// </summary>
        CostOptimized,

        /// <summary>
        /// 随机 - 随机分配请求
        /// </summary>
        Random
    }

    /// <summary>
    /// 压缩级别枚举
    /// 定义数据传输的压缩程度
    /// </summary>
    public enum CompressionLevel
    {
        /// <summary>
        /// 无压缩 - 不进行数据压缩
        /// </summary>
        None,

        /// <summary>
        /// 低压缩 - 轻度压缩，速度快
        /// </summary>
        Low,

        /// <summary>
        /// 中等压缩 - 平衡压缩率和速度
        /// </summary>
        Medium,

        /// <summary>
        /// 高压缩 - 高压缩率，速度较慢
        /// </summary>
        High,

        /// <summary>
        /// 最大压缩 - 最高压缩率，速度最慢
        /// </summary>
        Maximum
    }

    /// <summary>
    /// 日志级别枚举
    /// 定义系统日志的详细程度级别
    /// </summary>
    public enum LogLevel
    {
        /// <summary>
        /// 跟踪 - 最详细的日志信息，用于调试
        /// </summary>
        Trace,

        /// <summary>
        /// 调试 - 调试信息，开发时使用
        /// </summary>
        Debug,

        /// <summary>
        /// 信息 - 一般信息，记录正常操作
        /// </summary>
        Information,

        /// <summary>
        /// 警告 - 警告信息，可能的问题
        /// </summary>
        Warning,

        /// <summary>
        /// 错误 - 错误信息，需要关注的问题
        /// </summary>
        Error,

        /// <summary>
        /// 严重 - 严重错误，系统可能无法继续运行
        /// </summary>
        Critical,

        /// <summary>
        /// 无日志 - 不记录任何日志
        /// </summary>
        None
    }

    /// <summary>
    /// 熔断器状态枚举
    /// 定义熔断器的三种状态，用于服务保护
    /// </summary>
    public enum CircuitBreakerState
    {
        /// <summary>
        /// 关闭 - 正常状态，请求正常通过
        /// </summary>
        Closed,

        /// <summary>
        /// 开启 - 熔断状态，拒绝所有请求
        /// </summary>
        Open,

        /// <summary>
        /// 半开 - 试探状态，允许少量请求通过测试
        /// </summary>
        HalfOpen
    }

    /// <summary>
    /// 重试策略枚举
    /// 定义请求失败时的重试机制
    /// </summary>
    public enum RetryStrategy
    {
        /// <summary>
        /// 不重试 - 失败后不进行重试
        /// </summary>
        None,

        /// <summary>
        /// 固定间隔 - 固定时间间隔重试
        /// </summary>
        Fixed,

        /// <summary>
        /// 线性递增 - 重试间隔线性增加
        /// </summary>
        Linear,

        /// <summary>
        /// 指数退避 - 重试间隔指数增长
        /// </summary>
        Exponential,

        /// <summary>
        /// 自定义 - 用户自定义重试策略
        /// </summary>
        Custom
    }

    /// <summary>
    /// 队列溢出策略枚举
    /// 定义队列满时的处理策略
    /// </summary>
    public enum QueueOverflowStrategy
    {
        /// <summary>
        /// 丢弃 - 丢弃新的请求
        /// </summary>
        Drop,

        /// <summary>
        /// 阻塞 - 阻塞直到队列有空间
        /// </summary>
        Block,

        /// <summary>
        /// 扩展 - 动态扩展队列大小
        /// </summary>
        Expand,

        /// <summary>
        /// 拒绝 - 拒绝新的请求并返回错误
        /// </summary>
        Reject
    }

    /// <summary>
    /// 任务调度策略枚举
    /// 定义任务的调度执行方式
    /// </summary>
    public enum TaskSchedulingStrategy
    {
        /// <summary>
        /// 立即执行 - 任务立即执行
        /// </summary>
        Immediate,

        /// <summary>
        /// 批量执行 - 将任务批量处理
        /// </summary>
        Batched,

        /// <summary>
        /// 定时执行 - 按计划时间执行
        /// </summary>
        Scheduled,

        /// <summary>
        /// 优先级执行 - 按优先级顺序执行
        /// </summary>
        Priority,

        /// <summary>
        /// 自定义 - 用户自定义调度策略
        /// </summary>
        Custom
    }

    /// <summary>
    /// 任务执行顺序枚举
    /// 定义任务队列的执行顺序
    /// </summary>
    public enum TaskExecutionOrder
    {
        /// <summary>
        /// 先进先出 - 按到达顺序执行
        /// </summary>
        FIFO,

        /// <summary>
        /// 后进先出 - 最新任务优先执行
        /// </summary>
        LIFO,

        /// <summary>
        /// 优先级 - 按优先级高低执行
        /// </summary>
        Priority,

        /// <summary>
        /// 自定义 - 用户自定义执行顺序
        /// </summary>
        Custom
    }
}
