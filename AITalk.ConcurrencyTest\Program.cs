using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Linq;

namespace AITalk.ConcurrencyTest
{
    /// <summary>
    /// DeepSeek API并发测试程序
    /// 测试50个并发请求的性能和稳定性
    /// </summary>
    class Program
    {
        // DeepSeek API配置
        private static readonly string ApiKey = "sk-235870fcddb84a308771480cbac2ad22";
        private static readonly string BaseUrl = "https://api.deepseek.com/v1/chat/completions";
        private static readonly HttpClient httpClient = new HttpClient();

        /// <summary>
        /// 测试结果统计
        /// </summary>
        public class TestResult
        {
            public int RequestId { get; set; }
            public bool Success { get; set; }
            public long ResponseTimeMs { get; set; }
            public string Question { get; set; }
            public string Answer { get; set; }
            public string ErrorMessage { get; set; }
            public int TokenCount { get; set; }
        }

        /// <summary>
        /// 50个测试问题
        /// </summary>
        private static readonly string[] TestQuestions = {
            "什么是人工智能？",
            "解释一下机器学习的基本概念",
            "深度学习和传统机器学习有什么区别？",
            "什么是神经网络？",
            "解释一下自然语言处理",
            "什么是计算机视觉？",
            "强化学习是如何工作的？",
            "什么是大语言模型？",
            "解释一下Transformer架构",
            "什么是注意力机制？",
            "GPT模型的工作原理是什么？",
            "什么是BERT模型？",
            "解释一下卷积神经网络",
            "什么是循环神经网络？",
            "LSTM和GRU有什么区别？",
            "什么是生成对抗网络？",
            "解释一下监督学习",
            "什么是无监督学习？",
            "半监督学习的应用场景有哪些？",
            "什么是迁移学习？",
            "解释一下梯度下降算法",
            "什么是反向传播？",
            "过拟合和欠拟合是什么？",
            "如何防止过拟合？",
            "什么是正则化？",
            "解释一下批量归一化",
            "什么是Dropout？",
            "激活函数有哪些类型？",
            "ReLU函数的优缺点是什么？",
            "什么是损失函数？",
            "交叉熵损失函数如何计算？",
            "什么是优化器？",
            "Adam优化器的特点是什么？",
            "什么是学习率？",
            "如何选择合适的学习率？",
            "什么是数据增强？",
            "特征工程的重要性是什么？",
            "什么是降维？",
            "PCA算法的原理是什么？",
            "什么是聚类算法？",
            "K-means算法如何工作？",
            "什么是决策树？",
            "随机森林的优势是什么？",
            "什么是支持向量机？",
            "朴素贝叶斯算法的假设是什么？",
            "什么是集成学习？",
            "Boosting和Bagging的区别是什么？",
            "什么是模型评估？",
            "准确率和召回率的区别是什么？",
            "F1分数如何计算？"
        };

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== DeepSeek API 并发性能测试 ===");
            Console.WriteLine($"测试配置：");
            Console.WriteLine($"  • 并发请求数：50");
            Console.WriteLine($"  • API地址：{BaseUrl}");
            Console.WriteLine($"  • 模型：deepseek-chat");
            Console.WriteLine();

            // 配置HttpClient
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {ApiKey}");
            httpClient.Timeout = TimeSpan.FromMinutes(5); // 5分钟超时

            Console.WriteLine("🚀 开始并发测试...");
            var stopwatch = Stopwatch.StartNew();

            // 创建50个并发任务
            var tasks = new List<Task<TestResult>>();
            for (int i = 0; i < 50; i++)
            {
                int requestId = i + 1;
                string question = TestQuestions[i];
                tasks.Add(SendRequestAsync(requestId, question));
            }

            // 等待所有任务完成
            var results = await Task.WhenAll(tasks);
            stopwatch.Stop();

            // 分析结果
            AnalyzeResults(results, stopwatch.ElapsedMilliseconds);

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 发送单个API请求
        /// </summary>
        private static async Task<TestResult> SendRequestAsync(int requestId, string question)
        {
            var result = new TestResult
            {
                RequestId = requestId,
                Question = question
            };

            var requestStopwatch = Stopwatch.StartNew();

            try
            {
                Console.WriteLine($"📤 [请求{requestId:D2}] 发送问题: {question}");

                // 构建请求体
                var requestBody = new
                {
                    model = "deepseek-chat",
                    messages = new[]
                    {
                        new { role = "user", content = question }
                    },
                    max_tokens = 500,
                    temperature = 0.7
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                // 发送请求
                var response = await httpClient.PostAsync(BaseUrl, content);
                requestStopwatch.Stop();

                result.ResponseTimeMs = requestStopwatch.ElapsedMilliseconds;

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var responseObj = JsonSerializer.Deserialize<JsonElement>(responseJson);

                    if (responseObj.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
                    {
                        var firstChoice = choices[0];
                        if (firstChoice.TryGetProperty("message", out var message) &&
                            message.TryGetProperty("content", out var contentProp))
                        {
                            result.Answer = contentProp.GetString();
                            result.TokenCount = result.Answer?.Length ?? 0;
                            result.Success = true;

                            Console.WriteLine($"✅ [请求{requestId:D2}] 成功 ({result.ResponseTimeMs}ms) - {result.Answer?.Substring(0, Math.Min(50, result.Answer.Length))}...");
                        }
                    }
                }
                else
                {
                    result.ErrorMessage = $"HTTP {response.StatusCode}: {await response.Content.ReadAsStringAsync()}";
                    Console.WriteLine($"❌ [请求{requestId:D2}] 失败 ({result.ResponseTimeMs}ms) - {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                requestStopwatch.Stop();
                result.ResponseTimeMs = requestStopwatch.ElapsedMilliseconds;
                result.ErrorMessage = ex.Message;
                Console.WriteLine($"💥 [请求{requestId:D2}] 异常 ({result.ResponseTimeMs}ms) - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 分析测试结果
        /// </summary>
        private static void AnalyzeResults(TestResult[] results, long totalTimeMs)
        {
            Console.WriteLine("\n" + new string('=', 60));
            Console.WriteLine("📊 测试结果分析");
            Console.WriteLine(new string('=', 60));

            var successCount = results.Count(r => r.Success);
            var failureCount = results.Length - successCount;
            var successRate = (double)successCount / results.Length * 100;

            Console.WriteLine($"📈 总体统计:");
            Console.WriteLine($"  • 总请求数: {results.Length}");
            Console.WriteLine($"  • 成功请求: {successCount}");
            Console.WriteLine($"  • 失败请求: {failureCount}");
            Console.WriteLine($"  • 成功率: {successRate:F1}%");
            Console.WriteLine($"  • 总耗时: {totalTimeMs:N0} ms ({totalTimeMs / 1000.0:F1} 秒)");

            if (successCount > 0)
            {
                var successResults = results.Where(r => r.Success).ToArray();
                var avgResponseTime = successResults.Average(r => r.ResponseTimeMs);
                var minResponseTime = successResults.Min(r => r.ResponseTimeMs);
                var maxResponseTime = successResults.Max(r => r.ResponseTimeMs);
                var totalTokens = successResults.Sum(r => r.TokenCount);

                Console.WriteLine($"\n⏱️ 响应时间统计:");
                Console.WriteLine($"  • 平均响应时间: {avgResponseTime:F0} ms");
                Console.WriteLine($"  • 最快响应时间: {minResponseTime} ms");
                Console.WriteLine($"  • 最慢响应时间: {maxResponseTime} ms");
                Console.WriteLine($"  • 并发效率: {(double)successCount * avgResponseTime / totalTimeMs:F2}x");

                Console.WriteLine($"\n📝 内容统计:");
                Console.WriteLine($"  • 总字符数: {totalTokens:N0}");
                Console.WriteLine($"  • 平均字符数: {totalTokens / successCount:F0}");
                Console.WriteLine($"  • 总吞吐量: {totalTokens / (totalTimeMs / 1000.0):F0} 字符/秒");

                // 显示响应时间分布
                Console.WriteLine($"\n📊 响应时间分布:");
                var timeRanges = new[] { 1000, 2000, 3000, 5000, 10000, int.MaxValue };
                var rangeLabels = new[] { "<1s", "1-2s", "2-3s", "3-5s", "5-10s", ">10s" };

                for (int i = 0; i < timeRanges.Length; i++)
                {
                    var count = successResults.Count(r => r.ResponseTimeMs <= timeRanges[i] && 
                        (i == 0 || r.ResponseTimeMs > timeRanges[i - 1]));
                    var percentage = (double)count / successCount * 100;
                    Console.WriteLine($"  • {rangeLabels[i]}: {count} 个 ({percentage:F1}%)");
                }
            }

            if (failureCount > 0)
            {
                Console.WriteLine($"\n❌ 失败原因分析:");
                var failureResults = results.Where(r => !r.Success).ToArray();
                var errorGroups = failureResults.GroupBy(r => r.ErrorMessage?.Split(':')[0] ?? "Unknown")
                    .OrderByDescending(g => g.Count());

                foreach (var group in errorGroups)
                {
                    Console.WriteLine($"  • {group.Key}: {group.Count()} 个");
                }
            }

            Console.WriteLine($"\n🎯 性能评估:");
            if (successRate >= 95)
                Console.WriteLine($"  • 稳定性: 优秀 ✨");
            else if (successRate >= 90)
                Console.WriteLine($"  • 稳定性: 良好 ✅");
            else if (successRate >= 80)
                Console.WriteLine($"  • 稳定性: 一般 ⚠️");
            else
                Console.WriteLine($"  • 稳定性: 较差 ❌");

            if (successCount > 0)
            {
                var avgTime = results.Where(r => r.Success).Average(r => r.ResponseTimeMs);
                if (avgTime < 2000)
                    Console.WriteLine($"  • 响应速度: 优秀 🚀");
                else if (avgTime < 5000)
                    Console.WriteLine($"  • 响应速度: 良好 ✅");
                else if (avgTime < 10000)
                    Console.WriteLine($"  • 响应速度: 一般 ⚠️");
                else
                    Console.WriteLine($"  • 响应速度: 较慢 🐌");
            }

            var concurrencyEfficiency = (double)successCount * 50 / results.Length;
            if (concurrencyEfficiency >= 40)
                Console.WriteLine($"  • 并发处理: 优秀 💪");
            else if (concurrencyEfficiency >= 30)
                Console.WriteLine($"  • 并发处理: 良好 ✅");
            else if (concurrencyEfficiency >= 20)
                Console.WriteLine($"  • 并发处理: 一般 ⚠️");
            else
                Console.WriteLine($"  • 并发处理: 较差 ❌");
        }
    }
}
