using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// 对话基础信息
    /// </summary>
    public class ConversationInfo
    {
        /// <summary>
        /// 对话唯一标识
        /// </summary>
        public Guid ConversationId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; } = string.Empty;

        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; } = string.Empty;

        /// <summary>
        /// 对话标题
        /// </summary>
        public string Title { get; set; } = string.Empty;

        /// <summary>
        /// 对话描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 对话类型
        /// </summary>
        public ConversationType ConversationType { get; set; } = ConversationType.MultiRound;

        /// <summary>
        /// 优先级
        /// </summary>
        public ConversationPriority Priority { get; set; } = ConversationPriority.Normal;

        /// <summary>
        /// 状态
        /// </summary>
        public ConversationStatus Status { get; set; } = ConversationStatus.Created;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 完成时间
        /// </summary>
        public DateTime? CompletedAt { get; set; }

        /// <summary>
        /// 最后活动时间
        /// </summary>
        public DateTime LastActivityAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// 标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 父对话ID
        /// </summary>
        public Guid? ParentConversationId { get; set; }

        /// <summary>
        /// 子对话ID列表
        /// </summary>
        public List<Guid> ChildConversationIds { get; set; } = new();

        /// <summary>
        /// 是否归档
        /// </summary>
        public bool IsArchived { get; set; } = false;

        /// <summary>
        /// 归档原因
        /// </summary>
        public string ArchiveReason { get; set; } = string.Empty;

        /// <summary>
        /// 归档时间
        /// </summary>
        public DateTime? ArchivedAt { get; set; }

        /// <summary>
        /// 创建者
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 最后更新者
        /// </summary>
        public string LastUpdatedBy { get; set; } = string.Empty;

        /// <summary>
        /// 版本号
        /// </summary>
        public int Version { get; set; } = 1;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 删除者
        /// </summary>
        public string DeletedBy { get; set; } = string.Empty;

        /// <summary>
        /// 对话配置
        /// </summary>
        public ConversationConfiguration Configuration { get; set; } = new();

        /// <summary>
        /// 对话统计
        /// </summary>
        public ConversationStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 对话质量评估
        /// </summary>
        public ConversationQuality Quality { get; set; } = new();

        /// <summary>
        /// 更新状态
        /// </summary>
        public void UpdateStatus(ConversationStatus newStatus, string reason = "")
        {
            var oldStatus = Status;
            Status = newStatus;
            LastActivityAt = DateTime.UtcNow;

            // 记录状态变化
            if (!Metadata.ContainsKey("StatusHistory"))
            {
                Metadata["StatusHistory"] = new List<StatusChange>();
            }

            var statusHistory = (List<StatusChange>)Metadata["StatusHistory"];
            statusHistory.Add(new StatusChange
            {
                FromStatus = oldStatus,
                ToStatus = newStatus,
                Timestamp = DateTime.UtcNow,
                Reason = reason
            });

            // 设置特定状态的时间戳
            switch (newStatus)
            {
                case ConversationStatus.InProgress:
                    StartedAt ??= DateTime.UtcNow;
                    break;
                case ConversationStatus.Completed:
                case ConversationStatus.Failed:
                case ConversationStatus.Cancelled:
                    CompletedAt = DateTime.UtcNow;
                    break;
                case ConversationStatus.Archived:
                    IsArchived = true;
                    ArchivedAt = DateTime.UtcNow;
                    ArchiveReason = reason;
                    break;
            }
        }

        /// <summary>
        /// 计算对话持续时间
        /// </summary>
        public TimeSpan? GetDuration()
        {
            if (StartedAt.HasValue && CompletedAt.HasValue)
            {
                return CompletedAt.Value - StartedAt.Value;
            }
            else if (StartedAt.HasValue)
            {
                return DateTime.UtcNow - StartedAt.Value;
            }
            return null;
        }

        /// <summary>
        /// 检查是否过期
        /// </summary>
        public bool IsExpired()
        {
            return ExpiresAt.HasValue && DateTime.UtcNow > ExpiresAt.Value;
        }

        /// <summary>
        /// 检查是否活跃
        /// </summary>
        public bool IsActive()
        {
            return Status == ConversationStatus.InProgress && 
                   !IsExpired() && 
                   !IsDeleted && 
                   !IsArchived;
        }
    }

    /// <summary>
    /// 状态变化记录
    /// </summary>
    public class StatusChange
    {
        public ConversationStatus FromStatus { get; set; }
        public ConversationStatus ToStatus { get; set; }
        public DateTime Timestamp { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 对话配置
    /// </summary>
    public class ConversationConfiguration
    {
        public int MaxRounds { get; set; } = 50;
        public TimeSpan MaxDuration { get; set; } = TimeSpan.FromHours(1);
        public bool EnableAutoSave { get; set; } = true;
        public TimeSpan AutoSaveInterval { get; set; } = TimeSpan.FromMinutes(5);
        public bool EnableLogging { get; set; } = true;
        public bool EnableMetrics { get; set; } = true;
        public bool EnableQualityAssessment { get; set; } = true;
        public string PreferredLanguage { get; set; } = "en";
        public Dictionary<string, object> CustomSettings { get; set; } = new();
    }

    /// <summary>
    /// 对话统计
    /// </summary>
    public class ConversationStatistics
    {
        public int TotalRounds { get; set; } = 0;
        public int TotalMessages { get; set; } = 0;
        public int UserMessages { get; set; } = 0;
        public int AssistantMessages { get; set; } = 0;
        public int SystemMessages { get; set; } = 0;
        public long TotalTokens { get; set; } = 0;
        public long InputTokens { get; set; } = 0;
        public long OutputTokens { get; set; } = 0;
        public decimal TotalCost { get; set; } = 0;
        public TimeSpan TotalProcessingTime { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public int ErrorCount { get; set; } = 0;
        public int RetryCount { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 对话质量评估
    /// </summary>
    public class ConversationQuality
    {
        public double OverallScore { get; set; } = 0.0;
        public double RelevanceScore { get; set; } = 0.0;
        public double CoherenceScore { get; set; } = 0.0;
        public double CompletenessScore { get; set; } = 0.0;
        public double EfficiencyScore { get; set; } = 0.0;
        public double UserSatisfactionScore { get; set; } = 0.0;
        public List<string> QualityIssues { get; set; } = new();
        public List<string> Improvements { get; set; } = new();
        public DateTime LastAssessed { get; set; } = DateTime.UtcNow;
        public string AssessmentMethod { get; set; } = "Automatic";
        public Dictionary<string, double> DetailedScores { get; set; } = new();
    }
}
