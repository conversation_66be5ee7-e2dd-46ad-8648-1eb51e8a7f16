using System;
using System.Collections.Generic;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 使用统计信息
    /// </summary>
    public class UsageStatistics
    {
        /// <summary>
        /// 总请求数
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// 成功请求数
        /// </summary>
        public long TotalSuccessfulRequests { get; set; }

        /// <summary>
        /// 失败请求数
        /// </summary>
        public long TotalFailedRequests { get; set; }

        /// <summary>
        /// 总Token使用量
        /// </summary>
        public long TotalTokensUsed { get; set; }

        /// <summary>
        /// 输入Token使用量
        /// </summary>
        public long InputTokensUsed { get; set; }

        /// <summary>
        /// 输出Token使用量
        /// </summary>
        public long OutputTokensUsed { get; set; }

        /// <summary>
        /// 总费用
        /// </summary>
        public decimal TotalCost { get; set; }

        /// <summary>
        /// 每Token费用
        /// </summary>
        public decimal CostPerToken { get; set; }

        /// <summary>
        /// 每请求费用
        /// </summary>
        public decimal CostPerRequest { get; set; }

        /// <summary>
        /// 每日使用统计
        /// </summary>
        public Dictionary<DateTime, UsageDayData> DailyUsage { get; set; } = new();

        /// <summary>
        /// 每月使用统计
        /// </summary>
        public Dictionary<DateTime, UsageMonthData> MonthlyUsage { get; set; } = new();

        /// <summary>
        /// 每小时使用统计
        /// </summary>
        public Dictionary<DateTime, UsageHourData> HourlyUsage { get; set; } = new();

        /// <summary>
        /// 配额限制
        /// </summary>
        public long QuotaLimit { get; set; }

        /// <summary>
        /// 已使用配额
        /// </summary>
        public long QuotaUsed { get; set; }

        /// <summary>
        /// 配额重置日期
        /// </summary>
        public DateTime QuotaResetDate { get; set; }

        /// <summary>
        /// 每分钟速率限制
        /// </summary>
        public int RateLimitPerMinute { get; set; }

        /// <summary>
        /// 每小时速率限制
        /// </summary>
        public int RateLimitPerHour { get; set; }

        /// <summary>
        /// 每日速率限制
        /// </summary>
        public int RateLimitPerDay { get; set; }

        /// <summary>
        /// 平均请求大小（字节）
        /// </summary>
        public long AverageRequestSize { get; set; }

        /// <summary>
        /// 平均响应大小（字节）
        /// </summary>
        public long AverageResponseSize { get; set; }

        /// <summary>
        /// 总数据传输量（字节）
        /// </summary>
        public long TotalDataTransferred { get; set; }

        /// <summary>
        /// 峰值并发请求数
        /// </summary>
        public int PeakConcurrentRequests { get; set; }

        /// <summary>
        /// 峰值并发时间
        /// </summary>
        public DateTime PeakConcurrentTime { get; set; }

        /// <summary>
        /// 使用模式分析
        /// </summary>
        public UsagePatternAnalysis PatternAnalysis { get; set; } = new();

        /// <summary>
        /// 成本分析
        /// </summary>
        public CostAnalysis CostAnalysis { get; set; } = new();

        /// <summary>
        /// 统计更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 每日使用数据
    /// </summary>
    public class UsageDayData
    {
        public DateTime Date { get; set; }
        public long Requests { get; set; }
        public long SuccessfulRequests { get; set; }
        public long FailedRequests { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ErrorRate { get; set; }
        public int PeakConcurrentRequests { get; set; }
        public Dictionary<string, long> ModelUsage { get; set; } = new();
        public Dictionary<string, long> EndpointUsage { get; set; } = new();
    }

    /// <summary>
    /// 每月使用数据
    /// </summary>
    public class UsageMonthData
    {
        public DateTime Month { get; set; }
        public long Requests { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public double AverageErrorRate { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public int UniqueDaysActive { get; set; }
        public Dictionary<string, long> TopModels { get; set; } = new();
        public Dictionary<string, decimal> CostByModel { get; set; } = new();
    }

    /// <summary>
    /// 每小时使用数据
    /// </summary>
    public class UsageHourData
    {
        public DateTime Hour { get; set; }
        public long Requests { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public double ErrorRate { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public int ConcurrentRequests { get; set; }
    }

    /// <summary>
    /// 使用模式分析
    /// </summary>
    public class UsagePatternAnalysis
    {
        public Dictionary<int, double> HourlyDistribution { get; set; } = new(); // 小时分布
        public Dictionary<DayOfWeek, double> WeeklyDistribution { get; set; } = new(); // 周分布
        public Dictionary<string, double> ModelPreference { get; set; } = new(); // 模型偏好
        public Dictionary<string, double> EndpointUsage { get; set; } = new(); // 端点使用
        public List<UsageSpike> UsageSpikes { get; set; } = new(); // 使用峰值
        public List<UsageTrend> Trends { get; set; } = new(); // 使用趋势
    }

    /// <summary>
    /// 成本分析
    /// </summary>
    public class CostAnalysis
    {
        public decimal TotalCost { get; set; }
        public decimal AverageDailyCost { get; set; }
        public decimal ProjectedMonthlyCost { get; set; }
        public Dictionary<string, decimal> CostByModel { get; set; } = new();
        public Dictionary<string, decimal> CostByEndpoint { get; set; } = new();
        public List<CostOptimizationSuggestion> OptimizationSuggestions { get; set; } = new();
        public CostTrend CostTrend { get; set; } = new();
    }

    /// <summary>
    /// 使用峰值
    /// </summary>
    public class UsageSpike
    {
        public DateTime Timestamp { get; set; }
        public long RequestCount { get; set; }
        public TimeSpan Duration { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    /// <summary>
    /// 使用趋势
    /// </summary>
    public class UsageTrend
    {
        public string TrendType { get; set; } = string.Empty; // "increasing", "decreasing", "stable"
        public double TrendValue { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 成本优化建议
    /// </summary>
    public class CostOptimizationSuggestion
    {
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public decimal PotentialSavings { get; set; }
        public string Implementation { get; set; } = string.Empty;
        public int Priority { get; set; }
    }

    /// <summary>
    /// 成本趋势
    /// </summary>
    public class CostTrend
    {
        public string Direction { get; set; } = string.Empty; // "up", "down", "stable"
        public double PercentageChange { get; set; }
        public TimeSpan Period { get; set; }
        public decimal PreviousPeriodCost { get; set; }
        public decimal CurrentPeriodCost { get; set; }
    }
}
