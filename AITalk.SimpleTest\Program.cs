using System;
using System.Threading.Tasks;
using System.Text;
using System.Collections.Generic;

namespace AITalk.SimpleTest
{
    /// <summary>
    /// AI内容回调函数委托
    /// </summary>
    /// <param name="newContent">新接收到的内容片段</param>
    /// <param name="totalContent">累积的全部内容</param>
    public delegate void AIContentCallback(string newContent, string totalContent);

    /// <summary>
    /// 简化的AITalk流式传输演示类
    /// 展示流式传输和回调功能的核心概念
    /// </summary>
    public class SimpleAITalkData
    {
        /// <summary>
        /// AI流式传输内容缓存 - 使用StringBuilder实现动态内容累积
        /// </summary>
        public StringBuilder AIStreamContent { get; private set; } = new StringBuilder();

        /// <summary>
        /// AI最终响应内容
        /// </summary>
        public string AIResponse { get; set; } = string.Empty;

        /// <summary>
        /// 内容回调函数列表
        /// </summary>
        private readonly List<AIContentCallback> _contentCallbacks = new List<AIContentCallback>();

        /// <summary>
        /// 回调函数访问锁，确保线程安全
        /// </summary>
        private readonly object _callbackLock = new object();

        /// <summary>
        /// 注册AI内容回调函数
        /// 新注册的回调函数会立即接收到已有的内容
        /// </summary>
        /// <param name="callback">回调函数</param>
        public void RegisterContentCallback(AIContentCallback callback)
        {
            lock (_callbackLock)
            {
                _contentCallbacks.Add(callback);

                // 如果已有内容，立即回调给新注册的函数
                var currentContent = AIStreamContent.ToString();
                if (!string.IsNullOrEmpty(currentContent))
                {
                    try
                    {
                        callback(currentContent, currentContent);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"回调函数执行异常: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// 获取当前注册的回调函数数量
        /// </summary>
        public int GetCallbackCount()
        {
            lock (_callbackLock)
            {
                return _contentCallbacks.Count;
            }
        }

        /// <summary>
        /// 添加AI流式传输内容 - 核心方法
        /// </summary>
        /// <param name="newContent">新接收到的内容片段</param>
        public void AppendAIStreamContent(string newContent)
        {
            if (string.IsNullOrEmpty(newContent)) return;

            string fullContent;
            List<AIContentCallback> callbacksCopy;

            lock (_callbackLock)
            {
                AIStreamContent.Append(newContent);
                fullContent = AIStreamContent.ToString();
                callbacksCopy = new List<AIContentCallback>(_contentCallbacks);
            }

            // 在锁外执行回调，避免死锁
            foreach (var callback in callbacksCopy)
            {
                try
                {
                    callback(newContent, fullContent);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"回调函数执行异常: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 完成AI内容接收
        /// </summary>
        public void FinalizeAIContent()
        {
            lock (_callbackLock)
            {
                AIResponse = AIStreamContent.ToString();
            }
        }
    }

    /// <summary>
    /// 简化的AITalk测试程序
    /// 用于验证流式传输和回调功能的核心概念
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== AITalk 流式传输演示程序 ===");
            Console.WriteLine("展示AI对话系统的核心流式传输和回调功能");
            Console.WriteLine();

            // 测试流式传输功能
            await TestStreamingFeatures();

            // 测试枚举和设计思路
            TestDesignConcepts();

            Console.WriteLine();
            Console.WriteLine("=== 演示完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 测试流式传输功能
        /// </summary>
        static async Task TestStreamingFeatures()
        {
            Console.WriteLine("🌊 测试流式传输功能");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

            // 创建简化的AITalkData实例
            var aiTalkData = new SimpleAITalkData();

            Console.WriteLine("✅ 创建流式传输对话实例");

            // 注册回调函数1：实时显示
            aiTalkData.RegisterContentCallback((newContent, totalContent) =>
            {
                Console.WriteLine($"📝 [回调1] 新内容: \"{newContent}\"");
                Console.WriteLine($"📊 [回调1] 总长度: {totalContent.Length} 字符");
            });

            // 注册回调函数2：字数统计
            int wordCount = 0;
            aiTalkData.RegisterContentCallback((newContent, totalContent) =>
            {
                wordCount = totalContent.Split(new char[] { ' ', '，', '。', '（', '）' },
                    StringSplitOptions.RemoveEmptyEntries).Length;
                Console.WriteLine($"🔢 [回调2] 当前字数: {wordCount} 个词");
            });

            // 注册回调函数3：关键词检测
            var keywords = new List<string>();
            aiTalkData.RegisterContentCallback((newContent, totalContent) =>
            {
                var aiKeywords = new[] { "人工智能", "AI", "计算机", "智能", "机器" };
                foreach (var keyword in aiKeywords)
                {
                    if (totalContent.Contains(keyword) && !keywords.Contains(keyword))
                    {
                        keywords.Add(keyword);
                        Console.WriteLine($"🔍 [回调3] 发现关键词: {keyword}");
                    }
                }
            });

            Console.WriteLine($"✅ 注册了 {aiTalkData.GetCallbackCount()} 个回调函数");

            // 模拟流式传输
            string[] contentChunks = {
                "人工智能",
                "（Artificial Intelligence，AI）",
                "是计算机科学的一个分支，",
                "它企图了解智能的实质，",
                "并生产出一种新的能以人类智能相似的方式做出反应的智能机器。"
            };

            Console.WriteLine("\n🚀 开始模拟流式传输...");
            foreach (var chunk in contentChunks)
            {
                Console.WriteLine($"\n⏳ 传输中: \"{chunk}\"");
                await Task.Delay(800); // 模拟网络延迟
                aiTalkData.AppendAIStreamContent(chunk);
            }

            // 中途注册新的回调函数
            Console.WriteLine("\n🔄 模拟中途注册新的回调函数...");
            aiTalkData.RegisterContentCallback((newContent, totalContent) =>
            {
                Console.WriteLine($"🆕 [回调4] 中途加入，立即收到已有内容: {totalContent.Length} 字符");
            });

            // 继续添加内容
            Console.WriteLine("\n⏳ 继续传输...");
            await Task.Delay(800);
            aiTalkData.AppendAIStreamContent("这个定义涵盖了AI的核心概念和发展方向。");

            // 完成流式传输
            aiTalkData.FinalizeAIContent();

            Console.WriteLine($"\n✅ 流式传输完成");
            Console.WriteLine($"📊 最终内容长度: {aiTalkData.AIResponse.Length} 字符");
            Console.WriteLine($"📊 最终字数: {wordCount} 个词");
            Console.WriteLine($"📊 检测到关键词: {string.Join(", ", keywords)}");
            Console.WriteLine($"📊 注册的回调数量: {aiTalkData.GetCallbackCount()}");

            Console.WriteLine("\n📄 完整内容:");
            Console.WriteLine($"「{aiTalkData.AIResponse}」");
        }

        /// <summary>
        /// 测试设计概念和架构思路
        /// </summary>
        static void TestDesignConcepts()
        {
            Console.WriteLine("\n🏗️ AITalk系统设计概念演示");
            Console.WriteLine("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

            Console.WriteLine("📋 核心设计理念:");
            Console.WriteLine("   1. 分层架构 - 清晰的职责分离");
            Console.WriteLine("   2. 流式传输 - 实时响应用户");
            Console.WriteLine("   3. 回调机制 - 灵活的事件处理");
            Console.WriteLine("   4. 优先级调度 - 智能任务管理");
            Console.WriteLine("   5. 多服务支持 - 统一的API接口");

            Console.WriteLine("\n🔧 主要组件:");
            Console.WriteLine("   • AITalk - 对话处理核心");
            Console.WriteLine("   • AITalkService - 服务管理");
            Console.WriteLine("   • AITalkServiceData - 服务数据");
            Console.WriteLine("   • AITalkData - 对话数据（含流式传输）");
            Console.WriteLine("   • OpenAIApiManager - API管理器");

            Console.WriteLine("\n📊 支持的对话类型:");
            var conversationTypes = new[]
            {
                "SingleRound - 单轮对话",
                "MultiRound - 多轮对话",
                "TaskOriented - 任务导向",
                "Streaming - 流式传输",
                "Batch - 批处理"
            };
            foreach (var type in conversationTypes)
            {
                Console.WriteLine($"   • {type}");
            }

            Console.WriteLine("\n🎯 优先级调度:");
            var priorities = new[]
            {
                "Critical - 关键优先级（立即处理）",
                "High - 高优先级（优先处理）",
                "Normal - 普通优先级（正常顺序）",
                "Low - 低优先级（延后处理）"
            };
            foreach (var priority in priorities)
            {
                Console.WriteLine($"   • {priority}");
            }

            Console.WriteLine("\n🔌 支持的服务提供商:");
            var providers = new[]
            {
                "OpenAI - 官方OpenAI服务",
                "Azure - 微软Azure OpenAI",
                "Anthropic - Claude服务",
                "Google - Gemini服务",
                "LocalLLM - 本地模型",
                "CustomProvider - 自定义提供商"
            };
            foreach (var provider in providers)
            {
                Console.WriteLine($"   • {provider}");
            }

            Console.WriteLine("\n⚡ 核心算法:");
            Console.WriteLine("   • 优先级调度算法 - 多级队列调度");
            Console.WriteLine("   • 循环检测算法 - 基于相似度检测");
            Console.WriteLine("   • 负载均衡算法 - 加权轮询+健康检查");
            Console.WriteLine("   • 流式传输算法 - SSE数据流解析");

            Console.WriteLine("\n🛡️ 安全特性:");
            Console.WriteLine("   • 线程安全 - 所有关键操作都有锁保护");
            Console.WriteLine("   • 异常处理 - 单个回调失败不影响其他");
            Console.WriteLine("   • 循环防护 - 自动检测并打破无限循环");
            Console.WriteLine("   • 资源管理 - 自动清理和释放资源");

            Console.WriteLine("\n📈 性能优化:");
            Console.WriteLine("   • 并发执行 - 支持多任务同时处理");
            Console.WriteLine("   • 智能缓存 - StringBuilder动态内容累积");
            Console.WriteLine("   • 连接池 - 复用HTTP连接");
            Console.WriteLine("   • 压缩传输 - 减少网络带宽使用");

            Console.WriteLine("\n🎨 用户体验:");
            Console.WriteLine("   • 实时反馈 - 流式传输逐字显示");
            Console.WriteLine("   • 中途加入 - 新回调立即获得历史内容");
            Console.WriteLine("   • 进度跟踪 - 实时显示任务执行状态");
            Console.WriteLine("   • 错误恢复 - 智能重试和故障转移");
        }
    }
}
