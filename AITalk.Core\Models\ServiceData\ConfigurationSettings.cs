using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 配置设置
    /// </summary>
    public class ConfigurationSettings
    {
        /// <summary>
        /// 连接超时
        /// </summary>
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// 读取超时
        /// </summary>
        public TimeSpan ReadTimeout { get; set; } = TimeSpan.FromSeconds(60);

        /// <summary>
        /// 写入超时
        /// </summary>
        public TimeSpan WriteTimeout { get; set; } = TimeSpan.FromSeconds(60);

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryAttempts { get; set; } = 3;

        /// <summary>
        /// 重试延迟
        /// </summary>
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

        /// <summary>
        /// 重试退避倍数
        /// </summary>
        public double RetryBackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// 熔断器阈值
        /// </summary>
        public int CircuitBreakerThreshold { get; set; } = 5;

        /// <summary>
        /// 熔断器超时
        /// </summary>
        public TimeSpan CircuitBreakerTimeout { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// 健康检查间隔
        /// </summary>
        public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);

        /// <summary>
        /// 保活间隔
        /// </summary>
        public TimeSpan KeepAliveInterval { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// 最大连接池大小
        /// </summary>
        public int MaxConnectionPoolSize { get; set; } = 100;

        /// <summary>
        /// 连接池超时
        /// </summary>
        public TimeSpan ConnectionPoolTimeout { get; set; } = TimeSpan.FromSeconds(30);

        /// <summary>
        /// 启用压缩
        /// </summary>
        public bool EnableCompression { get; set; } = true;

        /// <summary>
        /// 压缩级别
        /// </summary>
        public CompressionLevel CompressionLevel { get; set; } = CompressionLevel.Medium;

        /// <summary>
        /// 启用缓存
        /// </summary>
        public bool EnableCaching { get; set; } = true;

        /// <summary>
        /// 缓存过期时间
        /// </summary>
        public TimeSpan CacheExpiry { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 日志级别
        /// </summary>
        public LogLevel LogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// 启用指标收集
        /// </summary>
        public bool EnableMetrics { get; set; } = true;

        /// <summary>
        /// 自定义设置
        /// </summary>
        public Dictionary<string, object> CustomSettings { get; set; } = new();

        /// <summary>
        /// 请求头设置
        /// </summary>
        public Dictionary<string, string> DefaultHeaders { get; set; } = new();

        /// <summary>
        /// 用户代理
        /// </summary>
        public string UserAgent { get; set; } = "AITalk/1.0";

        /// <summary>
        /// 最大请求大小（字节）
        /// </summary>
        public long MaxRequestSize { get; set; } = 10 * 1024 * 1024; // 10MB

        /// <summary>
        /// 最大响应大小（字节）
        /// </summary>
        public long MaxResponseSize { get; set; } = 50 * 1024 * 1024; // 50MB

        /// <summary>
        /// 启用SSL验证
        /// </summary>
        public bool EnableSslVerification { get; set; } = true;

        /// <summary>
        /// SSL协议版本
        /// </summary>
        public string SslProtocol { get; set; } = "TLS12";

        /// <summary>
        /// 代理设置
        /// </summary>
        public ProxySettings? ProxySettings { get; set; }

        /// <summary>
        /// 负载均衡设置
        /// </summary>
        public LoadBalancingSettings LoadBalancing { get; set; } = new();

        /// <summary>
        /// 重试策略设置
        /// </summary>
        public RetryPolicySettings RetryPolicy { get; set; } = new();

        /// <summary>
        /// 熔断器设置
        /// </summary>
        public CircuitBreakerSettings CircuitBreaker { get; set; } = new();

        /// <summary>
        /// 缓存设置
        /// </summary>
        public CacheSettings Cache { get; set; } = new();

        /// <summary>
        /// 监控设置
        /// </summary>
        public MonitoringSettings Monitoring { get; set; } = new();

        /// <summary>
        /// 安全设置
        /// </summary>
        public SecuritySettings Security { get; set; } = new();
    }

    /// <summary>
    /// 代理设置
    /// </summary>
    public class ProxySettings
    {
        public string Host { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public bool UseDefaultCredentials { get; set; } = false;
        public List<string> BypassList { get; set; } = new();
    }

    /// <summary>
    /// 负载均衡设置
    /// </summary>
    public class LoadBalancingSettings
    {
        public LoadBalancingStrategy Strategy { get; set; } = LoadBalancingStrategy.RoundRobin;
        public Dictionary<Guid, double> ServiceWeights { get; set; } = new();
        public bool EnableHealthBasedRouting { get; set; } = true;
        public bool EnableGeographicRouting { get; set; } = false;
        public bool EnableCostOptimization { get; set; } = false;
        public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);
    }

    /// <summary>
    /// 重试策略设置
    /// </summary>
    public class RetryPolicySettings
    {
        public RetryStrategy Strategy { get; set; } = RetryStrategy.Exponential;
        public int MaxAttempts { get; set; } = 3;
        public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
        public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
        public double BackoffMultiplier { get; set; } = 2.0;
        public double Jitter { get; set; } = 0.1;
        public List<string> RetryableErrorCodes { get; set; } = new();
        public List<string> NonRetryableErrorCodes { get; set; } = new();
    }

    /// <summary>
    /// 熔断器设置
    /// </summary>
    public class CircuitBreakerSettings
    {
        public int FailureThreshold { get; set; } = 5;
        public TimeSpan OpenTimeout { get; set; } = TimeSpan.FromMinutes(1);
        public int HalfOpenMaxCalls { get; set; } = 3;
        public double FailureRateThreshold { get; set; } = 0.5; // 50%
        public TimeSpan SamplingDuration { get; set; } = TimeSpan.FromMinutes(1);
        public int MinimumThroughput { get; set; } = 10;
    }

    /// <summary>
    /// 缓存设置
    /// </summary>
    public class CacheSettings
    {
        public CacheStrategy Strategy { get; set; } = CacheStrategy.Memory;
        public TimeSpan DefaultExpiry { get; set; } = TimeSpan.FromMinutes(5);
        public long MaxCacheSize { get; set; } = 100 * 1024 * 1024; // 100MB
        public int MaxCacheEntries { get; set; } = 10000;
        public bool EnableCompression { get; set; } = true;
        public string RedisConnectionString { get; set; } = string.Empty;
        public string CacheKeyPrefix { get; set; } = "AITalk:";
    }

    /// <summary>
    /// 监控设置
    /// </summary>
    public class MonitoringSettings
    {
        public bool EnablePerformanceMetrics { get; set; } = true;
        public bool EnableHealthChecks { get; set; } = true;
        public bool EnableTracing { get; set; } = true;
        public bool EnableLogging { get; set; } = true;
        public TimeSpan MetricsInterval { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);
        public string TracingEndpoint { get; set; } = string.Empty;
        public string MetricsEndpoint { get; set; } = string.Empty;
    }

    /// <summary>
    /// 安全设置
    /// </summary>
    public class SecuritySettings
    {
        public bool EnableEncryption { get; set; } = true;
        public string EncryptionKey { get; set; } = string.Empty;
        public bool EnableTokenRotation { get; set; } = true;
        public TimeSpan TokenRotationInterval { get; set; } = TimeSpan.FromHours(24);
        public bool EnableAuditLogging { get; set; } = true;
        public bool EnableRateLimiting { get; set; } = true;
        public Dictionary<string, int> RateLimits { get; set; } = new();
        public List<string> AllowedIpAddresses { get; set; } = new();
        public List<string> BlockedIpAddresses { get; set; } = new();
    }
}
