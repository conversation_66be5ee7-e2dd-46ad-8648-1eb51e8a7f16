using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 服务器基础信息
    /// </summary>
    public class ServerInfo
    {
        /// <summary>
        /// 服务器唯一标识
        /// </summary>
        public Guid ServerId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 服务器名称
        /// </summary>
        public string ServerName { get; set; } = string.Empty;

        /// <summary>
        /// 服务器类型
        /// </summary>
        public ServiceType ServerType { get; set; }

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// IP地址
        /// </summary>
        public string IPAddress { get; set; } = string.Empty;

        /// <summary>
        /// 端口号
        /// </summary>
        public int Port { get; set; }

        /// <summary>
        /// 协议类型
        /// </summary>
        public ProtocolType Protocol { get; set; } = ProtocolType.HTTPS;

        /// <summary>
        /// 地理区域
        /// </summary>
        public string Region { get; set; } = string.Empty;

        /// <summary>
        /// 数据中心
        /// </summary>
        public string DataCenter { get; set; } = string.Empty;

        /// <summary>
        /// API版本
        /// </summary>
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 支持的模型列表
        /// </summary>
        public List<string> SupportedModels { get; set; } = new();

        /// <summary>
        /// 最大并发请求数
        /// </summary>
        public int MaxConcurrentRequests { get; set; } = 10;

        /// <summary>
        /// 是否激活
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 服务器描述
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// 服务器标签
        /// </summary>
        public List<string> Tags { get; set; } = new();

        /// <summary>
        /// 自定义元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 服务器权重（用于负载均衡）
        /// </summary>
        public double Weight { get; set; } = 1.0;

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 是否为备用服务器
        /// </summary>
        public bool IsBackup { get; set; } = false;

        /// <summary>
        /// 主服务器ID（如果这是备用服务器）
        /// </summary>
        public Guid? PrimaryServerId { get; set; }

        /// <summary>
        /// 备用服务器ID列表
        /// </summary>
        public List<Guid> BackupServerIds { get; set; } = new();
    }
}
