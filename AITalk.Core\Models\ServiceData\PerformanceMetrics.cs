using System;
using System.Collections.Generic;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        /// <summary>
        /// 平均响应时间
        /// </summary>
        public TimeSpan AverageResponseTime { get; set; }

        /// <summary>
        /// 最小响应时间
        /// </summary>
        public TimeSpan MinResponseTime { get; set; }

        /// <summary>
        /// 最大响应时间
        /// </summary>
        public TimeSpan MaxResponseTime { get; set; }

        /// <summary>
        /// 95分位响应时间
        /// </summary>
        public TimeSpan P95ResponseTime { get; set; }

        /// <summary>
        /// 99分位响应时间
        /// </summary>
        public TimeSpan P99ResponseTime { get; set; }

        /// <summary>
        /// 每秒吞吐量
        /// </summary>
        public double ThroughputPerSecond { get; set; }

        /// <summary>
        /// 每分钟吞吐量
        /// </summary>
        public double ThroughputPerMinute { get; set; }

        /// <summary>
        /// 每小时吞吐量
        /// </summary>
        public double ThroughputPerHour { get; set; }

        /// <summary>
        /// 成功率百分比
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 错误率百分比
        /// </summary>
        public double ErrorRate { get; set; }

        /// <summary>
        /// 超时率百分比
        /// </summary>
        public double TimeoutRate { get; set; }

        /// <summary>
        /// 重试率百分比
        /// </summary>
        public double RetryRate { get; set; }

        /// <summary>
        /// 可用性百分比
        /// </summary>
        public double AvailabilityPercentage { get; set; }

        /// <summary>
        /// 最后测量时间
        /// </summary>
        public DateTime LastMeasuredAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 测量窗口
        /// </summary>
        public TimeSpan MeasurementWindow { get; set; } = TimeSpan.FromMinutes(5);

        /// <summary>
        /// 并发连接数
        /// </summary>
        public int ConcurrentConnections { get; set; }

        /// <summary>
        /// 最大并发连接数
        /// </summary>
        public int MaxConcurrentConnections { get; set; }

        /// <summary>
        /// 队列长度
        /// </summary>
        public int QueueLength { get; set; }

        /// <summary>
        /// 最大队列长度
        /// </summary>
        public int MaxQueueLength { get; set; }

        /// <summary>
        /// CPU使用率百分比
        /// </summary>
        public double CpuUsagePercentage { get; set; }

        /// <summary>
        /// 内存使用率百分比
        /// </summary>
        public double MemoryUsagePercentage { get; set; }

        /// <summary>
        /// 网络带宽使用率百分比
        /// </summary>
        public double NetworkUsagePercentage { get; set; }

        /// <summary>
        /// 磁盘使用率百分比
        /// </summary>
        public double DiskUsagePercentage { get; set; }

        /// <summary>
        /// 响应时间历史记录
        /// </summary>
        public List<ResponseTimeRecord> ResponseTimeHistory { get; set; } = new();

        /// <summary>
        /// 错误历史记录
        /// </summary>
        public List<ErrorRecord> ErrorHistory { get; set; } = new();

        /// <summary>
        /// 性能趋势数据
        /// </summary>
        public Dictionary<DateTime, PerformanceSnapshot> TrendData { get; set; } = new();

        /// <summary>
        /// 性能基准数据
        /// </summary>
        public PerformanceBenchmark Benchmark { get; set; } = new();

        /// <summary>
        /// 性能告警阈值
        /// </summary>
        public PerformanceThresholds Thresholds { get; set; } = new();
    }

    /// <summary>
    /// 响应时间记录
    /// </summary>
    public class ResponseTimeRecord
    {
        public DateTime Timestamp { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string Endpoint { get; set; } = string.Empty;
        public bool IsSuccess { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// 错误记录
    /// </summary>
    public class ErrorRecord
    {
        public DateTime Timestamp { get; set; }
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public int Count { get; set; } = 1;
    }

    /// <summary>
    /// 性能快照
    /// </summary>
    public class PerformanceSnapshot
    {
        public DateTime Timestamp { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public int ActiveConnections { get; set; }
    }

    /// <summary>
    /// 性能基准
    /// </summary>
    public class PerformanceBenchmark
    {
        public TimeSpan BaselineResponseTime { get; set; }
        public double BaselineThroughput { get; set; }
        public double BaselineErrorRate { get; set; }
        public DateTime BenchmarkDate { get; set; }
        public string BenchmarkVersion { get; set; } = string.Empty;
    }

    /// <summary>
    /// 性能阈值
    /// </summary>
    public class PerformanceThresholds
    {
        public TimeSpan MaxResponseTime { get; set; } = TimeSpan.FromSeconds(30);
        public double MinThroughput { get; set; } = 1.0;
        public double MaxErrorRate { get; set; } = 0.05; // 5%
        public double MaxCpuUsage { get; set; } = 0.8; // 80%
        public double MaxMemoryUsage { get; set; } = 0.8; // 80%
        public int MaxQueueLength { get; set; } = 100;
    }
}
