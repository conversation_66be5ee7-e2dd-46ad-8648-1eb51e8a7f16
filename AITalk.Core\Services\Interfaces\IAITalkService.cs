using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using AITalk.Core.Enums;
using AITalk.Core.Models;
using AITalk.Core.Models.ServiceData;

namespace AITalk.Core.Services.Interfaces
{
    /// <summary>
    /// AI对话服务接口
    /// </summary>
    public interface IAITalkService
    {
        #region 服务发现和注册

        /// <summary>
        /// 注册服务
        /// </summary>
        Task<bool> RegisterServiceAsync(AITalkServiceData serviceData);

        /// <summary>
        /// 注销服务
        /// </summary>
        Task<bool> UnregisterServiceAsync(Guid serviceId);

        /// <summary>
        /// 发现服务
        /// </summary>
        Task<List<AITalkServiceData>> DiscoverServicesAsync();

        /// <summary>
        /// 根据ID获取服务
        /// </summary>
        Task<AITalkServiceData?> GetServiceByIdAsync(Guid serviceId);

        /// <summary>
        /// 根据类型获取服务
        /// </summary>
        Task<List<AITalkServiceData>> GetServicesByTypeAsync(ServiceType serviceType);

        /// <summary>
        /// 获取健康的服务
        /// </summary>
        Task<List<AITalkServiceData>> GetHealthyServicesAsync();

        #endregion

        #region 负载均衡和服务选择

        /// <summary>
        /// 选择最佳服务
        /// </summary>
        Task<AITalkServiceData?> SelectBestServiceAsync(
            List<AITalkServiceData> availableServices, 
            LoadBalancingStrategy strategy = LoadBalancingStrategy.HealthBased);

        /// <summary>
        /// 选择服务用于特定任务
        /// </summary>
        Task<AITalkServiceData?> SelectServiceForTaskAsync(
            TaskType taskType, 
            Dictionary<string, object>? requirements = null);

        #endregion

        #region API调用

        /// <summary>
        /// 发送聊天完成请求
        /// </summary>
        Task<ApiResponse<ChatCompletionResponse>> SendChatCompletionAsync(
            AITalkServiceData service, 
            ChatCompletionRequest request);

        /// <summary>
        /// 发送流式请求
        /// </summary>
        IAsyncEnumerable<StreamingResponse> SendStreamingRequestAsync(
            AITalkServiceData service, 
            StreamingRequest request);

        #endregion

        #region 配置管理

        /// <summary>
        /// 更新服务配置
        /// </summary>
        Task<bool> UpdateServiceConfigurationAsync(Guid serviceId, ConfigurationSettings configuration);

        /// <summary>
        /// 获取服务配置
        /// </summary>
        Task<ConfigurationSettings?> GetServiceConfigurationAsync(Guid serviceId);

        #endregion

        #region 健康检查和监控

        /// <summary>
        /// 执行健康检查
        /// </summary>
        Task<HealthCheckResult> PerformHealthCheckAsync(Guid serviceId);

        /// <summary>
        /// 获取服务性能报告
        /// </summary>
        Task<PerformanceReport> GetPerformanceReportAsync(Guid serviceId, TimeSpan period);

        #endregion
    }

    /// <summary>
    /// 服务发现接口
    /// </summary>
    public interface IServiceDiscovery
    {
        Task<bool> RegisterServiceAsync(AITalkServiceData serviceData);
        Task<bool> UnregisterServiceAsync(Guid serviceId);
        Task<List<AITalkServiceData>> DiscoverServicesAsync();
        Task<AITalkServiceData?> GetServiceByIdAsync(Guid serviceId);
        Task<List<AITalkServiceData>> GetServicesByTypeAsync(ServiceType serviceType);
        Task<List<AITalkServiceData>> GetServicesByRegionAsync(string region);
        Task<List<AITalkServiceData>> GetHealthyServicesAsync();
        Task<List<AITalkServiceData>> GetServicesByCapabilityAsync(string capability);
        Task RefreshServiceRegistryAsync();
        Task<ValidationResult> ValidateServiceConfigurationAsync(AITalkServiceData serviceData);
        Task<List<Guid>> GetServiceDependenciesAsync(Guid serviceId);
        Task<bool> UpdateServiceMetadataAsync(Guid serviceId, Dictionary<string, object> metadata);
    }

    /// <summary>
    /// 负载均衡器接口
    /// </summary>
    public interface ILoadBalancer
    {
        AITalkServiceData SelectService(List<AITalkServiceData> services, LoadBalancingStrategy strategy);
        AITalkServiceData RoundRobinSelection(List<AITalkServiceData> services);
        AITalkServiceData WeightedRoundRobinSelection(List<AITalkServiceData> services);
        AITalkServiceData LeastConnectionsSelection(List<AITalkServiceData> services);
        AITalkServiceData ResponseTimeBasedSelection(List<AITalkServiceData> services);
        AITalkServiceData HealthBasedSelection(List<AITalkServiceData> services);
        AITalkServiceData GeographicProximitySelection(List<AITalkServiceData> services, string userLocation);
        AITalkServiceData CostOptimizedSelection(List<AITalkServiceData> services);
        Task UpdateServiceWeightsAsync(Dictionary<Guid, double> weights);
        Task<ServiceLoadInfo> GetServiceLoadAsync(Guid serviceId);
        Task EnableFailoverAsync(Guid primaryServiceId, List<Guid> backupServiceIds);
        Task<Guid> TriggerFailoverAsync(Guid failedServiceId);
        Task<List<FailoverEvent>> GetFailoverHistoryAsync();
    }

    /// <summary>
    /// API客户端接口
    /// </summary>
    public interface IApiClient
    {
        Task<ApiResponse<T>> SendRequestAsync<T>(AITalkServiceData service, ApiRequest request);
        Task<ApiResponse<ChatCompletionResponse>> SendChatCompletionAsync(AITalkServiceData service, ChatCompletionRequest request);
        IAsyncEnumerable<StreamingResponse> SendStreamingRequestAsync(AITalkServiceData service, StreamingRequest request);
        Task<ValidationResult> ValidateRequestAsync(ApiRequest request);
        Task<ApiResponse<T>> RetryWithBackoffAsync<T>(Func<Task<ApiResponse<T>>> operation, Models.ConversationData.RetryPolicy policy);
        Task<ApiResponse<T>> ImplementCircuitBreakerAsync<T>(Func<Task<ApiResponse<T>>> operation, Guid serviceId);
        Task CacheResponseAsync<T>(string cacheKey, T response, TimeSpan expiry);
        Task<T?> GetCachedResponseAsync<T>(string cacheKey);
        Task InvalidateCacheAsync(string pattern);
        Task LogRequestAsync(ApiRequest request, AITalkServiceData service);
        Task LogResponseAsync<T>(ApiResponse<T> response, TimeSpan duration);
    }

    /// <summary>
    /// 性能监控接口
    /// </summary>
    public interface IPerformanceMonitor
    {
        Task StartMonitoringAsync(Guid serviceId);
        Task StopMonitoringAsync(Guid serviceId);
        Task RecordRequestMetricsAsync(Guid serviceId, RequestMetrics metrics);
        Task RecordResponseMetricsAsync(Guid serviceId, ResponseMetrics metrics);
        Task<TimeSpan> CalculateAverageResponseTimeAsync(Guid serviceId, TimeSpan window);
        Task<double> CalculateThroughputAsync(Guid serviceId, TimeSpan window);
        Task<double> CalculateErrorRateAsync(Guid serviceId, TimeSpan window);
        Task<PerformanceReport> GeneratePerformanceReportAsync(Guid serviceId, TimeSpan period);
        Task<RealTimeMetrics> GetRealTimeMetricsAsync(Guid serviceId);
        Task SetPerformanceAlertsAsync(Guid serviceId, AlertConfiguration config);
        Task<List<TrendData>> GetPerformanceTrendsAsync(Guid serviceId, TimeSpan period);
        Task<ComparisonReport> CompareServicePerformanceAsync(List<Guid> serviceIds, TimeSpan period);
        Task<byte[]> ExportMetricsAsync(Guid serviceId, ExportFormat format, TimeSpan period);
        Task ArchiveOldMetricsAsync(TimeSpan retentionPeriod);
    }

    /// <summary>
    /// 配置管理器接口
    /// </summary>
    public interface IConfigurationManager
    {
        Task<ServiceConfiguration> LoadConfigurationAsync(string configPath);
        Task<bool> SaveConfigurationAsync(ServiceConfiguration config);
        Task<bool> UpdateServiceConfigurationAsync(Guid serviceId, ServiceConfiguration config);
        Task<ServiceConfiguration?> GetConfigurationAsync(Guid serviceId);
        Task<ValidationResult> ValidateConfigurationAsync(ServiceConfiguration config);
        Task<bool> ApplyConfigurationChangesAsync(Guid serviceId, ConfigurationSettings changes);
        Task<bool> RollbackConfigurationAsync(Guid serviceId, int version);
        Task<List<ConfigurationVersion>> GetConfigurationHistoryAsync(Guid serviceId);
        Task EnableHotReloadAsync(Guid serviceId);
        Task DisableHotReloadAsync(Guid serviceId);
        IObservable<ConfigurationChange> WatchConfigurationChangesAsync(string configPath);
        Task<ServiceConfiguration> EncryptSensitiveSettingsAsync(ServiceConfiguration config);
        Task<ServiceConfiguration> DecryptSensitiveSettingsAsync(ServiceConfiguration config);
        Task<string> BackupConfigurationAsync(Guid serviceId);
        Task<bool> RestoreConfigurationAsync(Guid serviceId, string backupPath);
    }

    /// <summary>
    /// 安全管理器接口
    /// </summary>
    public interface ISecurityManager
    {
        Task<AuthenticationResult> AuthenticateServiceAsync(Guid serviceId, AuthenticationInfo credentials);
        Task<TokenRefreshResult> RefreshTokenAsync(Guid serviceId);
        Task<ValidationResult> ValidateApiKeyAsync(string apiKey);
        Task<EncryptedCredentials> EncryptCredentialsAsync(AuthenticationInfo credentials);
        Task<AuthenticationInfo> DecryptCredentialsAsync(EncryptedCredentials credentials);
        Task<KeyRotationResult> RotateApiKeysAsync(Guid serviceId);
        Task<bool> CheckPermissionsAsync(Guid serviceId, string operation);
        Task<bool> GrantPermissionAsync(Guid serviceId, string permission);
        Task<bool> RevokePermissionAsync(Guid serviceId, string permission);
        Task<List<string>> GetServicePermissionsAsync(Guid serviceId);
        Task<List<SecurityEvent>> AuditSecurityEventsAsync(Guid serviceId, TimeSpan period);
        Task<List<AnomalyAlert>> DetectAnomalousActivityAsync(Guid serviceId);
        Task<bool> EnableTwoFactorAuthAsync(Guid serviceId);
        Task<CertificateValidationResult> ValidateCertificateAsync(System.Security.Cryptography.X509Certificates.X509Certificate2 certificate);
        Task<bool> UpdateSecurityPolicyAsync(Guid serviceId, SecurityPolicy policy);
    }
}
