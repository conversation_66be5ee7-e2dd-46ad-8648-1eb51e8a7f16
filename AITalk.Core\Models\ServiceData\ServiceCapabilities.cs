using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 服务能力信息
    /// </summary>
    public class ServiceCapabilities
    {
        /// <summary>
        /// 支持的功能列表
        /// </summary>
        public List<string> SupportedFeatures { get; set; } = new();

        /// <summary>
        /// 支持的模型类型
        /// </summary>
        public List<ModelType> SupportedModelTypes { get; set; } = new();

        /// <summary>
        /// 支持的端点类型
        /// </summary>
        public List<EndpointType> SupportedEndpoints { get; set; } = new();

        /// <summary>
        /// 支持流式响应
        /// </summary>
        public bool SupportsStreaming { get; set; } = false;

        /// <summary>
        /// 支持函数调用
        /// </summary>
        public bool SupportsFunctionCalling { get; set; } = false;

        /// <summary>
        /// 支持视觉输入
        /// </summary>
        public bool SupportsVision { get; set; } = false;

        /// <summary>
        /// 支持音频输入
        /// </summary>
        public bool SupportsAudio { get; set; } = false;

        /// <summary>
        /// 支持图像生成
        /// </summary>
        public bool SupportsImageGeneration { get; set; } = false;

        /// <summary>
        /// 支持文档处理
        /// </summary>
        public bool SupportsDocumentProcessing { get; set; } = false;

        /// <summary>
        /// 支持代码生成
        /// </summary>
        public bool SupportsCodeGeneration { get; set; } = false;

        /// <summary>
        /// 支持多语言
        /// </summary>
        public List<string> SupportedLanguages { get; set; } = new();

        /// <summary>
        /// 支持的输入格式
        /// </summary>
        public List<string> SupportedInputFormats { get; set; } = new();

        /// <summary>
        /// 支持的输出格式
        /// </summary>
        public List<string> SupportedOutputFormats { get; set; } = new();

        /// <summary>
        /// 最大上下文长度
        /// </summary>
        public int MaxContextLength { get; set; } = 4096;

        /// <summary>
        /// 最大输出长度
        /// </summary>
        public int MaxOutputLength { get; set; } = 2048;

        /// <summary>
        /// 支持的温度范围
        /// </summary>
        public (double Min, double Max) TemperatureRange { get; set; } = (0.0, 2.0);

        /// <summary>
        /// 支持的Top-P范围
        /// </summary>
        public (double Min, double Max) TopPRange { get; set; } = (0.0, 1.0);

        /// <summary>
        /// 支持批处理
        /// </summary>
        public bool SupportsBatchProcessing { get; set; } = false;

        /// <summary>
        /// 最大批处理大小
        /// </summary>
        public int MaxBatchSize { get; set; } = 1;

        /// <summary>
        /// 支持异步处理
        /// </summary>
        public bool SupportsAsyncProcessing { get; set; } = false;

        /// <summary>
        /// 支持缓存
        /// </summary>
        public bool SupportsResponseCaching { get; set; } = false;

        /// <summary>
        /// 支持压缩
        /// </summary>
        public bool SupportsCompression { get; set; } = false;

        /// <summary>
        /// 支持的压缩格式
        /// </summary>
        public List<string> SupportedCompressionFormats { get; set; } = new();

        /// <summary>
        /// 支持Webhook
        /// </summary>
        public bool SupportsWebhooks { get; set; } = false;

        /// <summary>
        /// 支持实时通信
        /// </summary>
        public bool SupportsRealTimeComm { get; set; } = false;

        /// <summary>
        /// 支持的认证方式
        /// </summary>
        public List<AuthMethod> SupportedAuthMethods { get; set; } = new();

        /// <summary>
        /// 支持自定义模型
        /// </summary>
        public bool SupportsCustomModels { get; set; } = false;

        /// <summary>
        /// 支持微调
        /// </summary>
        public bool SupportsFineTuning { get; set; } = false;

        /// <summary>
        /// 支持嵌入向量
        /// </summary>
        public bool SupportsEmbeddings { get; set; } = false;

        /// <summary>
        /// 嵌入向量维度
        /// </summary>
        public int EmbeddingDimensions { get; set; } = 0;

        /// <summary>
        /// 支持内容审核
        /// </summary>
        public bool SupportsModeration { get; set; } = false;

        /// <summary>
        /// 支持的安全级别
        /// </summary>
        public List<string> SupportedSecurityLevels { get; set; } = new();

        /// <summary>
        /// 地理限制
        /// </summary>
        public List<string> GeographicRestrictions { get; set; } = new();

        /// <summary>
        /// 数据保留政策
        /// </summary>
        public string DataRetentionPolicy { get; set; } = string.Empty;

        /// <summary>
        /// 隐私合规性
        /// </summary>
        public List<string> PrivacyCompliance { get; set; } = new();

        /// <summary>
        /// API版本兼容性
        /// </summary>
        public Dictionary<string, bool> ApiVersionCompatibility { get; set; } = new();

        /// <summary>
        /// 特殊功能
        /// </summary>
        public Dictionary<string, object> SpecialFeatures { get; set; } = new();

        /// <summary>
        /// 性能特征
        /// </summary>
        public PerformanceCharacteristics Performance { get; set; } = new();

        /// <summary>
        /// 可靠性特征
        /// </summary>
        public ReliabilityCharacteristics Reliability { get; set; } = new();

        /// <summary>
        /// 扩展性特征
        /// </summary>
        public ScalabilityCharacteristics Scalability { get; set; } = new();
    }

    /// <summary>
    /// 性能特征
    /// </summary>
    public class PerformanceCharacteristics
    {
        public TimeSpan TypicalResponseTime { get; set; }
        public double TypicalThroughput { get; set; }
        public double MaxThroughput { get; set; }
        public int ConcurrentRequestLimit { get; set; }
        public bool SupportsLoadBalancing { get; set; }
        public bool SupportsAutoScaling { get; set; }
    }

    /// <summary>
    /// 可靠性特征
    /// </summary>
    public class ReliabilityCharacteristics
    {
        public double UptimeGuarantee { get; set; } = 0.99; // 99%
        public bool HasRedundancy { get; set; }
        public bool SupportsFailover { get; set; }
        public TimeSpan RecoveryTime { get; set; }
        public bool HasBackupSystems { get; set; }
        public string DisasterRecoveryPlan { get; set; } = string.Empty;
    }

    /// <summary>
    /// 扩展性特征
    /// </summary>
    public class ScalabilityCharacteristics
    {
        public bool SupportsHorizontalScaling { get; set; }
        public bool SupportsVerticalScaling { get; set; }
        public int MaxInstances { get; set; }
        public TimeSpan ScalingTime { get; set; }
        public bool AutoScalingEnabled { get; set; }
        public Dictionary<string, int> ResourceLimits { get; set; } = new();
    }
}
