using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AITalk.Core.Enums;
using AITalk.Core.Models;
using AITalk.Core.Models.ConversationData;
using AITalk.Core.Services.Interfaces;

namespace AITalk.Core.Services
{
    /// <summary>
    /// AI对话类 - 负责具体的对话内容和多轮调度
    /// </summary>
    public class AITalk : IAITalk
    {
        private readonly AITalkData _data;
        private readonly IAITalkServiceManage _serviceManager;
        private readonly ITaskSchedulingEngine _schedulingEngine;
        private readonly ITaskExecutor _taskExecutor;
        private readonly IConversationFlowController _flowController;
        private readonly IResultAggregator _resultAggregator;
        private readonly ISelfLoopDetector _loopDetector;
        private readonly CancellationTokenSource _cancellationTokenSource;

        /// <summary>
        /// 对话ID
        /// </summary>
        public Guid ConversationId => _data.ConversationInfo.ConversationId;

        /// <summary>
        /// 任务队列 - 多轮调度的核心
        /// </summary>
        public List<AITask> TaskQueue => GetAllTasks();

        /// <summary>
        /// 当前执行任务
        /// </summary>
        public AITask? CurrentTask { get; private set; }

        /// <summary>
        /// 最大轮次限制
        /// </summary>
        public int MaxRounds { get; set; } = 50;

        /// <summary>
        /// 当前轮次
        /// </summary>
        public int CurrentRound => _data.GetCurrentRound();

        /// <summary>
        /// 是否完成
        /// </summary>
        public bool IsCompleted => _data.IsCompleted();

        /// <summary>
        /// 是否取消
        /// </summary>
        public bool IsCancelled => _cancellationTokenSource.Token.IsCancellationRequested;

        /// <summary>
        /// 最终结果
        /// </summary>
        public ConversationResult? FinalResult => _data.Result;

        /// <summary>
        /// 关联的数据对象
        /// </summary>
        public AITalkData Data => _data;

        public AITalk(
            AITalkData data,
            IAITalkServiceManage serviceManager,
            ITaskSchedulingEngine schedulingEngine,
            ITaskExecutor taskExecutor,
            IConversationFlowController flowController,
            IResultAggregator resultAggregator,
            ISelfLoopDetector loopDetector)
        {
            _data = data ?? throw new ArgumentNullException(nameof(data));
            _serviceManager = serviceManager ?? throw new ArgumentNullException(nameof(serviceManager));
            _schedulingEngine = schedulingEngine ?? throw new ArgumentNullException(nameof(schedulingEngine));
            _taskExecutor = taskExecutor ?? throw new ArgumentNullException(nameof(taskExecutor));
            _flowController = flowController ?? throw new ArgumentNullException(nameof(flowController));
            _resultAggregator = resultAggregator ?? throw new ArgumentNullException(nameof(resultAggregator));
            _loopDetector = loopDetector ?? throw new ArgumentNullException(nameof(loopDetector));
            _cancellationTokenSource = new CancellationTokenSource();

            MaxRounds = _data.Configuration.MaxRounds;
        }

        /// <summary>
        /// 开始对话
        /// </summary>
        public async Task<ConversationResult> StartConversationAsync(string initialInput)
        {
            try
            {
                _data.UpdateStatus(ConversationStatus.InProgress, "Conversation started");
                
                // 初始化任务调度
                var initialTasks = await _schedulingEngine.ScheduleInitialTasksAsync(initialInput);
                foreach (var task in initialTasks)
                {
                    _data.AddTask(task);
                }

                // 开始对话流程控制
                return await _flowController.StartConversationAsync(initialInput);
            }
            catch (Exception ex)
            {
                _data.UpdateStatus(ConversationStatus.Failed, $"Conversation failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 处理当前轮次
        /// </summary>
        public async Task<RoundResult> ProcessCurrentRoundAsync()
        {
            var roundResult = new RoundResult
            {
                RoundNumber = CurrentRound
            };

            try
            {
                // 检查是否超过最大轮次
                if (CurrentRound > MaxRounds)
                {
                    roundResult.ShouldContinue = false;
                    roundResult.WarningsGenerated.Add($"Reached maximum rounds limit: {MaxRounds}");
                    return roundResult;
                }

                // 检查循环
                var loopDetectionResult = await _loopDetector.DetectLoopAsync(GetTaskHistory());
                if (loopDetectionResult == LoopDetectionResult.InfiniteLoop)
                {
                    await HandleInfiniteLoopAsync();
                    roundResult.ShouldContinue = false;
                    roundResult.WarningsGenerated.Add("Infinite loop detected and handled");
                    return roundResult;
                }

                // 获取可执行的任务
                var executableTasks = await GetExecutableTasksAsync();
                if (executableTasks.Count == 0)
                {
                    roundResult.ShouldContinue = false;
                    return roundResult;
                }

                // 执行任务
                var startTime = DateTime.UtcNow;
                foreach (var task in executableTasks)
                {
                    if (_cancellationTokenSource.Token.IsCancellationRequested)
                        break;

                    try
                    {
                        CurrentTask = task;
                        var executionResult = await ExecuteTaskAsync(task);
                        
                        if (executionResult.IsSuccess)
                        {
                            roundResult.CompletedTasks.Add(task);
                            
                            // 根据任务结果生成新任务
                            var newTasks = await _schedulingEngine.ScheduleNextTasksAsync(task);
                            foreach (var newTask in newTasks)
                            {
                                newTask.Round = CurrentRound + 1;
                                _data.AddTask(newTask);
                                roundResult.GeneratedTasks.Add(newTask);
                            }
                        }
                        else
                        {
                            roundResult.FailedTasks.Add(task);
                            if (executionResult.Error != null)
                            {
                                roundResult.ErrorsEncountered.Add(executionResult.Error);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        roundResult.FailedTasks.Add(task);
                        roundResult.ErrorsEncountered.Add(ex);
                        _data.FailTask(task, ex);
                    }
                }

                CurrentTask = null;
                
                // 计算轮次指标
                var duration = DateTime.UtcNow - startTime;
                roundResult.RoundMetrics = CalculateRoundMetrics(roundResult, duration);
                
                // 确定是否继续
                roundResult.ShouldContinue = await ShouldContinueConversationAsync(roundResult);
                
                return roundResult;
            }
            catch (Exception ex)
            {
                roundResult.ErrorsEncountered.Add(ex);
                roundResult.ShouldContinue = false;
                return roundResult;
            }
        }

        /// <summary>
        /// 执行单个任务
        /// </summary>
        public async Task<TaskExecutionResult> ExecuteTaskAsync(AITask task)
        {
            try
            {
                // 开始执行任务
                if (!_data.StartTask(task))
                {
                    return new TaskExecutionResult
                    {
                        TaskId = task.TaskId,
                        IsSuccess = false,
                        Error = new InvalidOperationException("Failed to start task execution")
                    };
                }

                // 执行任务
                var result = await _taskExecutor.ExecuteTaskAsync(task);
                
                // 完成任务
                if (result.IsSuccess)
                {
                    _data.CompleteTask(task, result.Output);
                }
                else
                {
                    _data.FailTask(task, result.Error ?? new Exception("Task execution failed"));
                }

                return result;
            }
            catch (Exception ex)
            {
                _data.FailTask(task, ex);
                return new TaskExecutionResult
                {
                    TaskId = task.TaskId,
                    IsSuccess = false,
                    Error = ex
                };
            }
        }

        /// <summary>
        /// 添加用户消息
        /// </summary>
        public async Task AddUserMessageAsync(string content)
        {
            var message = new ConversationMessage
            {
                Role = MessageRole.User,
                Content = content,
                ContentType = MessageContentType.Text
            };

            _data.AddMessage(message);
            
            // 根据用户输入生成新任务
            var newTasks = await _schedulingEngine.ScheduleInitialTasksAsync(content);
            foreach (var task in newTasks)
            {
                task.Round = CurrentRound + 1;
                _data.AddTask(task);
            }
        }

        /// <summary>
        /// 添加助手消息
        /// </summary>
        public void AddAssistantMessage(string content, Guid? serviceId = null, string? modelName = null)
        {
            var message = new ConversationMessage
            {
                Role = MessageRole.Assistant,
                Content = content,
                ContentType = MessageContentType.Text,
                ServiceId = serviceId,
                ModelName = modelName ?? string.Empty
            };

            _data.AddMessage(message);
        }

        /// <summary>
        /// 取消对话
        /// </summary>
        public async Task CancelConversationAsync(string reason = "")
        {
            _cancellationTokenSource.Cancel();
            _data.UpdateStatus(ConversationStatus.Cancelled, reason);
            
            // 取消所有执行中的任务
            var executingTasks = _data.TaskQueue.ExecutingTasks.Values.ToList();
            foreach (var task in executingTasks)
            {
                _data.CancelTask(task, reason);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 暂停对话
        /// </summary>
        public async Task PauseConversationAsync()
        {
            _data.UpdateStatus(ConversationStatus.Paused, "Conversation paused by user");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 恢复对话
        /// </summary>
        public async Task ResumeConversationAsync()
        {
            _data.UpdateStatus(ConversationStatus.InProgress, "Conversation resumed");
            await Task.CompletedTask;
        }

        /// <summary>
        /// 获取对话进度
        /// </summary>
        public ConversationProgress GetProgress()
        {
            var allTasks = GetAllTasks();
            var completedTasks = allTasks.Count(t => t.Status == Enums.TaskStatus.Completed);
            var pendingTasks = allTasks.Count(t => t.Status == Enums.TaskStatus.Pending);
            var failedTasks = allTasks.Count(t => t.Status == Enums.TaskStatus.Failed);

            return new ConversationProgress
            {
                ConversationId = ConversationId,
                CurrentRound = CurrentRound,
                MaxRounds = MaxRounds,
                ProgressPercentage = MaxRounds > 0 ? (double)CurrentRound / MaxRounds * 100 : 0,
                CompletedTasks = completedTasks,
                PendingTasks = pendingTasks,
                FailedTasks = failedTasks,
                ElapsedTime = _data.ConversationInfo.GetDuration() ?? TimeSpan.Zero,
                CurrentPhase = _data.State.CurrentPhase
            };
        }

        /// <summary>
        /// 获取所有任务
        /// </summary>
        private List<AITask> GetAllTasks()
        {
            var allTasks = new List<AITask>();
            allTasks.AddRange(_data.TaskQueue.PendingTasks);
            allTasks.AddRange(_data.TaskQueue.ExecutingTasks.Values);
            allTasks.AddRange(_data.TaskQueue.CompletedTasks);
            allTasks.AddRange(_data.TaskQueue.FailedTasks);
            allTasks.AddRange(_data.TaskQueue.CancelledTasks);
            return allTasks;
        }

        /// <summary>
        /// 获取任务历史
        /// </summary>
        private List<AITask> GetTaskHistory()
        {
            var history = new List<AITask>();
            history.AddRange(_data.TaskQueue.CompletedTasks);
            history.AddRange(_data.TaskQueue.FailedTasks);
            history.AddRange(_data.TaskQueue.CancelledTasks);
            return history.OrderBy(t => t.CreatedAt).ToList();
        }

        /// <summary>
        /// 获取可执行的任务
        /// </summary>
        private async Task<List<AITask>> GetExecutableTasksAsync()
        {
            var executableTasks = new List<AITask>();
            var allTasks = GetAllTasks().ToDictionary(t => t.TaskId, t => t);

            while (_data.TaskQueue.PendingTasks.Count > 0 && 
                   _data.TaskQueue.CurrentConcurrentTasks < _data.TaskQueue.MaxConcurrentTasks)
            {
                var task = _data.GetNextTask();
                if (task == null) break;

                // 检查依赖是否满足
                if (task.AreDependenciesSatisfied(allTasks))
                {
                    executableTasks.Add(task);
                }
                else
                {
                    // 重新放回队列
                    _data.AddTask(task);
                    break; // 避免无限循环
                }
            }

            return executableTasks;
        }

        /// <summary>
        /// 处理无限循环
        /// </summary>
        private async Task HandleInfiniteLoopAsync()
        {
            var taskHistory = GetTaskHistory();
            var loopPattern = await _loopDetector.AnalyzeLoopPatternAsync(taskHistory);
            var strategies = await _loopDetector.GetLoopBreakingStrategiesAsync(loopPattern);

            foreach (var strategy in strategies)
            {
                var success = await _loopDetector.ApplyLoopBreakingStrategyAsync(strategy);
                if (success)
                {
                    break;
                }
            }
        }

        /// <summary>
        /// 计算轮次指标
        /// </summary>
        private RoundMetrics CalculateRoundMetrics(RoundResult roundResult, TimeSpan duration)
        {
            var totalTasks = roundResult.CompletedTasks.Count + roundResult.FailedTasks.Count;
            
            return new RoundMetrics
            {
                Duration = duration,
                TasksExecuted = totalTasks,
                TasksSucceeded = roundResult.CompletedTasks.Count,
                TasksFailed = roundResult.FailedTasks.Count,
                SuccessRate = totalTasks > 0 ? (double)roundResult.CompletedTasks.Count / totalTasks : 0,
                AverageTaskDuration = totalTasks > 0 ? duration.TotalMilliseconds / totalTasks : 0
            };
        }

        /// <summary>
        /// 判断是否应该继续对话
        /// </summary>
        private async Task<bool> ShouldContinueConversationAsync(RoundResult roundResult)
        {
            // 检查是否有待处理的任务
            if (_data.TaskQueue.PendingTasks.Count > 0)
                return true;

            // 检查是否需要用户输入
            if (roundResult.UserInteractionRequired)
                return true;

            // 检查是否达到最大轮次
            if (CurrentRound >= MaxRounds)
                return false;

            // 检查是否有严重错误
            if (roundResult.ErrorsEncountered.Count > 3)
                return false;

            return false;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
        }
    }
}
