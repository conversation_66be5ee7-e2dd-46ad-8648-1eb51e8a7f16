using System;
using System.Collections.Generic;
using System.Linq;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// 消息历史
    /// </summary>
    public class MessageHistory
    {
        /// <summary>
        /// 消息列表
        /// </summary>
        public List<ConversationMessage> Messages { get; set; } = new();

        /// <summary>
        /// 总消息数
        /// </summary>
        public int TotalMessageCount => Messages.Count;

        /// <summary>
        /// 用户消息数
        /// </summary>
        public int UserMessageCount => Messages.Count(m => m.Role == MessageRole.User);

        /// <summary>
        /// 助手消息数
        /// </summary>
        public int AssistantMessageCount => Messages.Count(m => m.Role == MessageRole.Assistant);

        /// <summary>
        /// 系统消息数
        /// </summary>
        public int SystemMessageCount => Messages.Count(m => m.Role == MessageRole.System);

        /// <summary>
        /// 最大历史长度
        /// </summary>
        public int MaxHistoryLength { get; set; } = 1000;

        /// <summary>
        /// 是否启用压缩
        /// </summary>
        public bool CompressionEnabled { get; set; } = false;

        /// <summary>
        /// 压缩比率
        /// </summary>
        public double CompressionRatio { get; set; } = 0.0;

        /// <summary>
        /// 是否启用加密
        /// </summary>
        public bool EncryptionEnabled { get; set; } = false;

        /// <summary>
        /// 是否启用备份
        /// </summary>
        public bool BackupEnabled { get; set; } = true;

        /// <summary>
        /// 最后备份时间
        /// </summary>
        public DateTime? LastBackupAt { get; set; }

        /// <summary>
        /// 历史统计信息
        /// </summary>
        public MessageHistoryStatistics Statistics { get; set; } = new();

        /// <summary>
        /// 添加消息
        /// </summary>
        public void AddMessage(ConversationMessage message)
        {
            if (message == null) return;

            Messages.Add(message);
            
            // 检查是否超过最大长度
            if (Messages.Count > MaxHistoryLength)
            {
                // 移除最旧的消息（保留系统消息）
                var oldestNonSystemMessage = Messages
                    .Where(m => m.Role != MessageRole.System)
                    .OrderBy(m => m.Timestamp)
                    .FirstOrDefault();
                
                if (oldestNonSystemMessage != null)
                {
                    Messages.Remove(oldestNonSystemMessage);
                }
            }

            UpdateStatistics();
        }

        /// <summary>
        /// 获取最近的消息
        /// </summary>
        public List<ConversationMessage> GetRecentMessages(int count)
        {
            return Messages
                .OrderByDescending(m => m.Timestamp)
                .Take(count)
                .OrderBy(m => m.Timestamp)
                .ToList();
        }

        /// <summary>
        /// 获取指定角色的消息
        /// </summary>
        public List<ConversationMessage> GetMessagesByRole(MessageRole role)
        {
            return Messages.Where(m => m.Role == role).ToList();
        }

        /// <summary>
        /// 获取指定时间范围的消息
        /// </summary>
        public List<ConversationMessage> GetMessagesByTimeRange(DateTime start, DateTime end)
        {
            return Messages.Where(m => m.Timestamp >= start && m.Timestamp <= end).ToList();
        }

        /// <summary>
        /// 搜索消息
        /// </summary>
        public List<ConversationMessage> SearchMessages(string keyword, bool caseSensitive = false)
        {
            var comparison = caseSensitive ? StringComparison.Ordinal : StringComparison.OrdinalIgnoreCase;
            return Messages.Where(m => m.Content.Contains(keyword, comparison)).ToList();
        }

        /// <summary>
        /// 获取消息统计
        /// </summary>
        public MessageStatistics GetMessageStatistics()
        {
            return new MessageStatistics
            {
                TotalMessages = TotalMessageCount,
                UserMessages = UserMessageCount,
                AssistantMessages = AssistantMessageCount,
                SystemMessages = SystemMessageCount,
                TotalTokens = Messages.Sum(m => m.TokenCount),
                AverageTokensPerMessage = Messages.Count > 0 ? Messages.Average(m => m.TokenCount) : 0,
                TotalProcessingTime = TimeSpan.FromMilliseconds(Messages.Sum(m => m.ProcessingTime.TotalMilliseconds)),
                AverageProcessingTime = Messages.Count > 0 ? 
                    TimeSpan.FromMilliseconds(Messages.Average(m => m.ProcessingTime.TotalMilliseconds)) : 
                    TimeSpan.Zero,
                FirstMessageTime = Messages.Count > 0 ? Messages.Min(m => m.Timestamp) : DateTime.MinValue,
                LastMessageTime = Messages.Count > 0 ? Messages.Max(m => m.Timestamp) : DateTime.MinValue
            };
        }

        /// <summary>
        /// 清理旧消息
        /// </summary>
        public int CleanupOldMessages(TimeSpan maxAge)
        {
            var cutoffTime = DateTime.UtcNow - maxAge;
            var oldMessages = Messages.Where(m => m.Timestamp < cutoffTime && m.Role != MessageRole.System).ToList();
            
            foreach (var message in oldMessages)
            {
                Messages.Remove(message);
            }

            if (oldMessages.Count > 0)
            {
                UpdateStatistics();
            }

            return oldMessages.Count;
        }

        /// <summary>
        /// 压缩历史记录
        /// </summary>
        public void CompressHistory()
        {
            if (!CompressionEnabled) return;

            // 这里可以实现消息压缩逻辑
            // 例如：合并连续的系统消息，摘要化长对话等
            
            // 简单示例：移除重复的系统消息
            var systemMessages = Messages.Where(m => m.Role == MessageRole.System).ToList();
            var duplicateSystemMessages = systemMessages
                .GroupBy(m => m.Content)
                .Where(g => g.Count() > 1)
                .SelectMany(g => g.Skip(1))
                .ToList();

            foreach (var duplicate in duplicateSystemMessages)
            {
                Messages.Remove(duplicate);
            }

            UpdateStatistics();
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            Statistics.LastUpdated = DateTime.UtcNow;
            Statistics.TotalMessages = TotalMessageCount;
            Statistics.MessagesByRole = new Dictionary<MessageRole, int>
            {
                { MessageRole.User, UserMessageCount },
                { MessageRole.Assistant, AssistantMessageCount },
                { MessageRole.System, SystemMessageCount },
                { MessageRole.Function, Messages.Count(m => m.Role == MessageRole.Function) },
                { MessageRole.Tool, Messages.Count(m => m.Role == MessageRole.Tool) }
            };
            Statistics.TotalTokens = Messages.Sum(m => m.TokenCount);
            Statistics.TotalSize = Messages.Sum(m => m.Content.Length);
        }

        /// <summary>
        /// 导出消息历史
        /// </summary>
        public string ExportToJson()
        {
            return System.Text.Json.JsonSerializer.Serialize(Messages, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            });
        }

        /// <summary>
        /// 从JSON导入消息历史
        /// </summary>
        public void ImportFromJson(string json)
        {
            try
            {
                var importedMessages = System.Text.Json.JsonSerializer.Deserialize<List<ConversationMessage>>(json);
                if (importedMessages != null)
                {
                    Messages.AddRange(importedMessages);
                    UpdateStatistics();
                }
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException("Failed to import message history from JSON", ex);
            }
        }
    }

    /// <summary>
    /// 消息历史统计信息
    /// </summary>
    public class MessageHistoryStatistics
    {
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public int TotalMessages { get; set; } = 0;
        public Dictionary<MessageRole, int> MessagesByRole { get; set; } = new();
        public long TotalTokens { get; set; } = 0;
        public long TotalSize { get; set; } = 0;
        public double AverageMessageSize { get; set; } = 0.0;
        public TimeSpan TotalProcessingTime { get; set; }
        public Dictionary<string, int> MessagesByContentType { get; set; } = new();
        public Dictionary<DateTime, int> MessagesByHour { get; set; } = new();
        public Dictionary<DateTime, int> MessagesByDay { get; set; } = new();
    }

    /// <summary>
    /// 消息统计
    /// </summary>
    public class MessageStatistics
    {
        public int TotalMessages { get; set; }
        public int UserMessages { get; set; }
        public int AssistantMessages { get; set; }
        public int SystemMessages { get; set; }
        public long TotalTokens { get; set; }
        public double AverageTokensPerMessage { get; set; }
        public TimeSpan TotalProcessingTime { get; set; }
        public TimeSpan AverageProcessingTime { get; set; }
        public DateTime FirstMessageTime { get; set; }
        public DateTime LastMessageTime { get; set; }
    }
}
