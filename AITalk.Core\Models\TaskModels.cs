using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models
{
    /// <summary>
    /// 执行计划
    /// </summary>
    public class ExecutionPlan
    {
        public Guid PlanId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<ExecutionPhase> Phases { get; set; } = new();
        public Dictionary<Guid, List<Guid>> TaskDependencies { get; set; } = new();
        public TimeSpan EstimatedDuration { get; set; }
        public int EstimatedRounds { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public string CreatedBy { get; set; } = string.Empty;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 执行阶段
    /// </summary>
    public class ExecutionPhase
    {
        public Guid PhaseId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int Order { get; set; }
        public List<Guid> TaskIds { get; set; } = new();
        public bool CanExecuteInParallel { get; set; } = false;
        public TimeSpan EstimatedDuration { get; set; }
        public Dictionary<string, object> Configuration { get; set; } = new();
    }

    /// <summary>
    /// 任务失败策略
    /// </summary>
    public class TaskFailureStrategy
    {
        public string StrategyType { get; set; } = string.Empty; // "retry", "skip", "abort", "fallback"
        public int MaxRetries { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
        public bool UseExponentialBackoff { get; set; } = true;
        public List<Guid> FallbackTaskIds { get; set; } = new();
        public bool NotifyOnFailure { get; set; } = true;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 任务完成结果
    /// </summary>
    public class TaskCompletionResult
    {
        public Guid TaskId { get; set; }
        public bool IsSuccess { get; set; }
        public object? Output { get; set; }
        public TimeSpan ExecutionTime { get; set; }
        public long TokensUsed { get; set; }
        public decimal Cost { get; set; }
        public double QualityScore { get; set; }
        public List<string> GeneratedInsights { get; set; } = new();
        public Dictionary<string, object> Metrics { get; set; } = new();
        public DateTime CompletedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 分析结果
    /// </summary>
    public class AnalysisResult
    {
        public Guid AnalysisId { get; set; } = Guid.NewGuid();
        public string AnalysisType { get; set; } = string.Empty;
        public List<Guid> AnalyzedTaskIds { get; set; } = new();
        public Dictionary<string, object> Findings { get; set; } = new();
        public List<string> Insights { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public double ConfidenceScore { get; set; } = 0.0;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> RawData { get; set; } = new();
    }

    /// <summary>
    /// 部分结果
    /// </summary>
    public class PartialResult
    {
        public Guid ResultId { get; set; } = Guid.NewGuid();
        public Guid SourceTaskId { get; set; }
        public object? Data { get; set; }
        public double CompletionPercentage { get; set; }
        public double ConfidenceScore { get; set; }
        public string ResultType { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 结果摘要
    /// </summary>
    public class ResultSummary
    {
        public Guid SummaryId { get; set; } = Guid.NewGuid();
        public string Title { get; set; } = string.Empty;
        public string Summary { get; set; } = string.Empty;
        public List<string> KeyPoints { get; set; } = new();
        public List<string> MainFindings { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
        public double OverallConfidence { get; set; } = 0.0;
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Statistics { get; set; } = new();
    }

    /// <summary>
    /// 结果缺口
    /// </summary>
    public class ResultGap
    {
        public Guid GapId { get; set; } = Guid.NewGuid();
        public string GapType { get; set; } = string.Empty; // "missing_data", "incomplete_analysis", "low_confidence"
        public string Description { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty; // "low", "medium", "high", "critical"
        public List<string> SuggestedActions { get; set; } = new();
        public Dictionary<string, object> Details { get; set; } = new();
        public DateTime IdentifiedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 改进建议
    /// </summary>
    public class ImprovementSuggestion
    {
        public Guid SuggestionId { get; set; } = Guid.NewGuid();
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // "performance", "quality", "efficiency", "accuracy"
        public int Priority { get; set; } = 0; // 1-10, 10 being highest
        public double ExpectedImprovement { get; set; } = 0.0; // percentage
        public string Implementation { get; set; } = string.Empty;
        public TimeSpan EstimatedEffort { get; set; }
        public List<string> Prerequisites { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 服务统计
    /// </summary>
    public class ServiceStatistics
    {
        public int TotalServices { get; set; }
        public int HealthyServices { get; set; }
        public int UnhealthyServices { get; set; }
        public int ServicesInMaintenance { get; set; }
        public double AverageResponseTime { get; set; }
        public double AverageSuccessRate { get; set; }
        public long TotalRequestsProcessed { get; set; }
        public long TotalTokensUsed { get; set; }
        public decimal TotalCost { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        public Dictionary<ServiceType, int> ServicesByType { get; set; } = new();
        public Dictionary<string, double> PerformanceMetrics { get; set; } = new();
    }

    /// <summary>
    /// 服务告警
    /// </summary>
    public class ServiceAlert
    {
        public Guid AlertId { get; set; } = Guid.NewGuid();
        public Guid ServiceId { get; set; }
        public string ServiceName { get; set; } = string.Empty;
        public string AlertType { get; set; } = string.Empty; // "performance", "health", "security", "quota"
        public string Severity { get; set; } = string.Empty; // "low", "medium", "high", "critical"
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime TriggeredAt { get; set; } = DateTime.UtcNow;
        public DateTime? ResolvedAt { get; set; }
        public bool IsResolved { get; set; } = false;
        public string Resolution { get; set; } = string.Empty;
        public List<string> RecommendedActions { get; set; } = new();
        public Dictionary<string, object> AlertData { get; set; } = new();
        public int OccurrenceCount { get; set; } = 1;
        public DateTime LastOccurrence { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 工作流状态
    /// </summary>
    public class WorkflowState
    {
        public Guid WorkflowId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string CurrentStep { get; set; } = string.Empty;
        public List<string> CompletedSteps { get; set; } = new();
        public List<string> RemainingSteps { get; set; } = new();
        public Dictionary<string, object> StateData { get; set; } = new();
        public DateTime StartedAt { get; set; } = DateTime.UtcNow;
        public DateTime? CompletedAt { get; set; }
        public TimeSpan ElapsedTime => DateTime.UtcNow - StartedAt;
        public double ProgressPercentage { get; set; } = 0.0;
        public bool IsCompleted { get; set; } = false;
        public bool IsFailed { get; set; } = false;
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 决策点
    /// </summary>
    public class DecisionPoint
    {
        public Guid DecisionId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<DecisionOption> Options { get; set; } = new();
        public DecisionCriteria Criteria { get; set; } = new();
        public DecisionOption? SelectedOption { get; set; }
        public string SelectionReason { get; set; } = string.Empty;
        public double ConfidenceScore { get; set; } = 0.0;
        public DateTime DecisionMadeAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 决策选项
    /// </summary>
    public class DecisionOption
    {
        public Guid OptionId { get; set; } = Guid.NewGuid();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public double Score { get; set; } = 0.0;
        public double Weight { get; set; } = 1.0;
        public List<string> Pros { get; set; } = new();
        public List<string> Cons { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
        public TimeSpan EstimatedExecutionTime { get; set; }
        public decimal EstimatedCost { get; set; }
        public double RiskLevel { get; set; } = 0.0;
    }

    /// <summary>
    /// 决策标准
    /// </summary>
    public class DecisionCriteria
    {
        public List<string> RequiredCapabilities { get; set; } = new();
        public Dictionary<string, double> WeightedFactors { get; set; } = new();
        public double MinimumScore { get; set; } = 0.0;
        public TimeSpan MaxExecutionTime { get; set; } = TimeSpan.MaxValue;
        public decimal MaxCost { get; set; } = decimal.MaxValue;
        public double MaxRiskLevel { get; set; } = 1.0;
        public List<string> ExclusionCriteria { get; set; } = new();
        public Dictionary<string, object> CustomCriteria { get; set; } = new();
    }

    /// <summary>
    /// 质量指标
    /// </summary>
    public class QualityMetrics
    {
        public double Accuracy { get; set; } = 0.0;
        public double Precision { get; set; } = 0.0;
        public double Recall { get; set; } = 0.0;
        public double F1Score { get; set; } = 0.0;
        public double Completeness { get; set; } = 0.0;
        public double Consistency { get; set; } = 0.0;
        public double Relevance { get; set; } = 0.0;
        public double Coherence { get; set; } = 0.0;
        public double Fluency { get; set; } = 0.0;
        public double OverallQuality { get; set; } = 0.0;
        public DateTime MeasuredAt { get; set; } = DateTime.UtcNow;
        public string MeasurementMethod { get; set; } = string.Empty;
        public Dictionary<string, double> CustomMetrics { get; set; } = new();
    }
}
