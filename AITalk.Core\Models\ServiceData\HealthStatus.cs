using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 健康状态
    /// </summary>
    public class HealthStatus
    {
        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; } = true;

        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; } = true;

        /// <summary>
        /// 最后健康检查时间
        /// </summary>
        public DateTime LastHealthCheck { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 最后成功请求时间
        /// </summary>
        public DateTime? LastSuccessfulRequest { get; set; }

        /// <summary>
        /// 最后失败请求时间
        /// </summary>
        public DateTime? LastFailedRequest { get; set; }

        /// <summary>
        /// 连续失败次数
        /// </summary>
        public int ConsecutiveFailures { get; set; } = 0;

        /// <summary>
        /// 健康评分 (0-100)
        /// </summary>
        public double HealthScore { get; set; } = 100.0;

        /// <summary>
        /// 状态消息
        /// </summary>
        public string StatusMessage { get; set; } = "Healthy";

        /// <summary>
        /// 错误日志
        /// </summary>
        public List<ErrorLogEntry> ErrorLogs { get; set; } = new();

        /// <summary>
        /// 警告日志
        /// </summary>
        public List<WarningLogEntry> WarningLogs { get; set; } = new();

        /// <summary>
        /// 维护模式
        /// </summary>
        public bool MaintenanceMode { get; set; } = false;

        /// <summary>
        /// 维护开始时间
        /// </summary>
        public DateTime? MaintenanceStartTime { get; set; }

        /// <summary>
        /// 维护结束时间
        /// </summary>
        public DateTime? MaintenanceEndTime { get; set; }

        /// <summary>
        /// 预期停机时间
        /// </summary>
        public TimeSpan? ExpectedDowntime { get; set; }

        /// <summary>
        /// 告警阈值设置
        /// </summary>
        public AlertThresholdSettings AlertThresholds { get; set; } = new();

        /// <summary>
        /// 服务状态
        /// </summary>
        public ServiceStatus ServiceStatus { get; set; } = ServiceStatus.Online;

        /// <summary>
        /// 详细状态信息
        /// </summary>
        public Dictionary<string, object> DetailedStatus { get; set; } = new();

        /// <summary>
        /// 依赖服务状态
        /// </summary>
        public Dictionary<string, bool> DependencyStatus { get; set; } = new();

        /// <summary>
        /// 性能指标
        /// </summary>
        public HealthPerformanceMetrics Performance { get; set; } = new();

        /// <summary>
        /// 可用性统计
        /// </summary>
        public AvailabilityStatistics Availability { get; set; } = new();

        /// <summary>
        /// 健康检查历史
        /// </summary>
        public List<HealthCheckRecord> HealthCheckHistory { get; set; } = new();

        /// <summary>
        /// 自动恢复设置
        /// </summary>
        public AutoRecoverySettings AutoRecovery { get; set; } = new();
    }

    /// <summary>
    /// 错误日志条目
    /// </summary>
    public class ErrorLogEntry
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string StackTrace { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public LogLevel Severity { get; set; } = LogLevel.Error;
        public Dictionary<string, object> Context { get; set; } = new();
        public bool IsResolved { get; set; } = false;
        public DateTime? ResolvedAt { get; set; }
        public string Resolution { get; set; } = string.Empty;
    }

    /// <summary>
    /// 警告日志条目
    /// </summary>
    public class WarningLogEntry
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string WarningCode { get; set; } = string.Empty;
        public string WarningMessage { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public LogLevel Severity { get; set; } = LogLevel.Warning;
        public Dictionary<string, object> Context { get; set; } = new();
        public bool IsAcknowledged { get; set; } = false;
        public DateTime? AcknowledgedAt { get; set; }
        public string AcknowledgedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 告警阈值设置
    /// </summary>
    public class AlertThresholdSettings
    {
        public double ErrorRateThreshold { get; set; } = 0.05; // 5%
        public TimeSpan ResponseTimeThreshold { get; set; } = TimeSpan.FromSeconds(30);
        public double HealthScoreThreshold { get; set; } = 80.0;
        public int ConsecutiveFailuresThreshold { get; set; } = 5;
        public double CpuUsageThreshold { get; set; } = 0.8; // 80%
        public double MemoryUsageThreshold { get; set; } = 0.8; // 80%
        public double DiskUsageThreshold { get; set; } = 0.9; // 90%
        public int QueueLengthThreshold { get; set; } = 100;
        public TimeSpan UnavailabilityThreshold { get; set; } = TimeSpan.FromMinutes(5);
    }

    /// <summary>
    /// 健康性能指标
    /// </summary>
    public class HealthPerformanceMetrics
    {
        public TimeSpan AverageResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double ErrorRate { get; set; }
        public double CpuUsage { get; set; }
        public double MemoryUsage { get; set; }
        public double DiskUsage { get; set; }
        public double NetworkUsage { get; set; }
        public int ActiveConnections { get; set; }
        public int QueueLength { get; set; }
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 可用性统计
    /// </summary>
    public class AvailabilityStatistics
    {
        public double UptimePercentage { get; set; } = 100.0;
        public TimeSpan TotalUptime { get; set; }
        public TimeSpan TotalDowntime { get; set; }
        public DateTime LastDowntime { get; set; }
        public TimeSpan LongestDowntime { get; set; }
        public int DowntimeIncidents { get; set; } = 0;
        public double MTBF { get; set; } = 0; // Mean Time Between Failures
        public double MTTR { get; set; } = 0; // Mean Time To Recovery
        public List<DowntimeIncident> DowntimeHistory { get; set; } = new();
    }

    /// <summary>
    /// 停机事件
    /// </summary>
    public class DowntimeIncident
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
        public string Reason { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public string Resolution { get; set; } = string.Empty;
        public bool IsPlanned { get; set; } = false;
        public LogLevel Severity { get; set; } = LogLevel.Error;
    }

    /// <summary>
    /// 健康检查记录
    /// </summary>
    public class HealthCheckRecord
    {
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool IsHealthy { get; set; }
        public double HealthScore { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string Status { get; set; } = string.Empty;
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// 自动恢复设置
    /// </summary>
    public class AutoRecoverySettings
    {
        public bool EnableAutoRecovery { get; set; } = true;
        public int MaxRecoveryAttempts { get; set; } = 3;
        public TimeSpan RecoveryInterval { get; set; } = TimeSpan.FromMinutes(5);
        public List<string> RecoveryActions { get; set; } = new();
        public bool EnableFailover { get; set; } = true;
        public List<Guid> FailoverTargets { get; set; } = new();
        public bool NotifyOnRecovery { get; set; } = true;
        public List<string> NotificationChannels { get; set; } = new();
    }
}
