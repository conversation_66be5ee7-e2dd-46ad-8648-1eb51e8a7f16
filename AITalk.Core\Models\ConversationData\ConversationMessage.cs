using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.ConversationData
{
    /// <summary>
    /// 对话消息
    /// </summary>
    public class ConversationMessage
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public Guid MessageId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 消息角色
        /// </summary>
        public MessageRole Role { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 内容类型
        /// </summary>
        public MessageContentType ContentType { get; set; } = MessageContentType.Text;

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Token数量
        /// </summary>
        public int TokenCount { get; set; } = 0;

        /// <summary>
        /// 处理时间
        /// </summary>
        public TimeSpan ProcessingTime { get; set; }

        /// <summary>
        /// 使用的服务ID
        /// </summary>
        public Guid? ServiceId { get; set; }

        /// <summary>
        /// 使用的模型名称
        /// </summary>
        public string ModelName { get; set; } = string.Empty;

        /// <summary>
        /// 温度参数
        /// </summary>
        public double? Temperature { get; set; }

        /// <summary>
        /// 最大Token数
        /// </summary>
        public int? MaxTokens { get; set; }

        /// <summary>
        /// 附件列表
        /// </summary>
        public List<MessageAttachment> Attachments { get; set; } = new();

        /// <summary>
        /// 消息元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// 是否已编辑
        /// </summary>
        public bool IsEdited { get; set; } = false;

        /// <summary>
        /// 编辑历史
        /// </summary>
        public List<MessageEdit> EditHistory { get; set; } = new();

        /// <summary>
        /// 消息反应
        /// </summary>
        public List<MessageReaction> Reactions { get; set; } = new();

        /// <summary>
        /// 消息引用
        /// </summary>
        public List<MessageReference> References { get; set; } = new();

        /// <summary>
        /// 父消息ID（用于回复）
        /// </summary>
        public Guid? ParentMessageId { get; set; }

        /// <summary>
        /// 子消息ID列表
        /// </summary>
        public List<Guid> ChildMessageIds { get; set; } = new();

        /// <summary>
        /// 消息优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 消息状态
        /// </summary>
        public MessageStatus Status { get; set; } = MessageStatus.Sent;

        /// <summary>
        /// 错误信息（如果有）
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 是否已删除（软删除）
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 删除时间
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// 消息质量评分
        /// </summary>
        public MessageQuality Quality { get; set; } = new();

        /// <summary>
        /// 消息安全信息
        /// </summary>
        public MessageSecurity Security { get; set; } = new();

        /// <summary>
        /// 编辑消息
        /// </summary>
        public void EditContent(string newContent, string reason = "")
        {
            if (string.IsNullOrEmpty(newContent)) return;

            // 记录编辑历史
            EditHistory.Add(new MessageEdit
            {
                OriginalContent = Content,
                NewContent = newContent,
                EditedAt = DateTime.UtcNow,
                Reason = reason
            });

            Content = newContent;
            IsEdited = true;
            Timestamp = DateTime.UtcNow; // 更新时间戳
        }

        /// <summary>
        /// 添加反应
        /// </summary>
        public void AddReaction(string reactionType, string userId)
        {
            var existingReaction = Reactions.Find(r => r.Type == reactionType && r.UserId == userId);
            if (existingReaction == null)
            {
                Reactions.Add(new MessageReaction
                {
                    Type = reactionType,
                    UserId = userId,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// 移除反应
        /// </summary>
        public void RemoveReaction(string reactionType, string userId)
        {
            var reaction = Reactions.Find(r => r.Type == reactionType && r.UserId == userId);
            if (reaction != null)
            {
                Reactions.Remove(reaction);
            }
        }

        /// <summary>
        /// 添加引用
        /// </summary>
        public void AddReference(Guid referencedMessageId, string referenceType = "reply")
        {
            if (!References.Exists(r => r.ReferencedMessageId == referencedMessageId))
            {
                References.Add(new MessageReference
                {
                    ReferencedMessageId = referencedMessageId,
                    ReferenceType = referenceType,
                    CreatedAt = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// 添加附件
        /// </summary>
        public void AddAttachment(MessageAttachment attachment)
        {
            if (attachment != null)
            {
                Attachments.Add(attachment);
            }
        }

        /// <summary>
        /// 计算内容长度
        /// </summary>
        public int GetContentLength()
        {
            return Content?.Length ?? 0;
        }

        /// <summary>
        /// 获取消息摘要
        /// </summary>
        public string GetSummary(int maxLength = 100)
        {
            if (string.IsNullOrEmpty(Content)) return string.Empty;
            
            if (Content.Length <= maxLength) return Content;
            
            return Content.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// 检查是否包含敏感内容
        /// </summary>
        public bool ContainsSensitiveContent()
        {
            return Security.HasSensitiveContent || Security.IsFiltered;
        }

        /// <summary>
        /// 克隆消息
        /// </summary>
        public ConversationMessage Clone()
        {
            return new ConversationMessage
            {
                MessageId = Guid.NewGuid(),
                Role = Role,
                Content = Content,
                ContentType = ContentType,
                Timestamp = DateTime.UtcNow,
                TokenCount = TokenCount,
                ServiceId = ServiceId,
                ModelName = ModelName,
                Temperature = Temperature,
                MaxTokens = MaxTokens,
                Attachments = new List<MessageAttachment>(Attachments),
                Metadata = new Dictionary<string, object>(Metadata),
                Priority = Priority,
                Status = MessageStatus.Sent
            };
        }
    }

    /// <summary>
    /// 消息状态枚举
    /// </summary>
    public enum MessageStatus
    {
        Draft,
        Sending,
        Sent,
        Delivered,
        Read,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 消息附件
    /// </summary>
    public class MessageAttachment
    {
        public Guid AttachmentId { get; set; } = Guid.NewGuid();
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long Size { get; set; } = 0;
        public string Url { get; set; } = string.Empty;
        public byte[] Data { get; set; } = Array.Empty<byte>();
        public Dictionary<string, object> Metadata { get; set; } = new();
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 消息编辑记录
    /// </summary>
    public class MessageEdit
    {
        public Guid EditId { get; set; } = Guid.NewGuid();
        public string OriginalContent { get; set; } = string.Empty;
        public string NewContent { get; set; } = string.Empty;
        public DateTime EditedAt { get; set; } = DateTime.UtcNow;
        public string Reason { get; set; } = string.Empty;
        public string EditedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// 消息反应
    /// </summary>
    public class MessageReaction
    {
        public Guid ReactionId { get; set; } = Guid.NewGuid();
        public string Type { get; set; } = string.Empty; // like, dislike, love, laugh, etc.
        public string UserId { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 消息引用
    /// </summary>
    public class MessageReference
    {
        public Guid ReferenceId { get; set; } = Guid.NewGuid();
        public Guid ReferencedMessageId { get; set; }
        public string ReferenceType { get; set; } = string.Empty; // reply, quote, mention, etc.
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 消息质量
    /// </summary>
    public class MessageQuality
    {
        public double OverallScore { get; set; } = 0.0;
        public double RelevanceScore { get; set; } = 0.0;
        public double ClarityScore { get; set; } = 0.0;
        public double CompletenessScore { get; set; } = 0.0;
        public List<string> QualityIssues { get; set; } = new();
        public DateTime LastAssessed { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 消息安全
    /// </summary>
    public class MessageSecurity
    {
        public bool IsEncrypted { get; set; } = false;
        public bool IsFiltered { get; set; } = false;
        public bool HasSensitiveContent { get; set; } = false;
        public List<string> FilterReasons { get; set; } = new();
        public string SecurityLevel { get; set; } = "Normal";
        public DateTime LastSecurityCheck { get; set; } = DateTime.UtcNow;
    }
}
