using System;
using System.Collections.Generic;

namespace AITalk.Core.Models.ServiceData
{
    /// <summary>
    /// 服务限制信息
    /// </summary>
    public class ServiceLimits
    {
        /// <summary>
        /// 请求速率限制
        /// </summary>
        public RateLimits RateLimits { get; set; } = new();

        /// <summary>
        /// 配额限制
        /// </summary>
        public QuotaLimits QuotaLimits { get; set; } = new();

        /// <summary>
        /// 大小限制
        /// </summary>
        public SizeLimits SizeLimits { get; set; } = new();

        /// <summary>
        /// 并发限制
        /// </summary>
        public ConcurrencyLimits ConcurrencyLimits { get; set; } = new();

        /// <summary>
        /// 时间限制
        /// </summary>
        public TimeLimits TimeLimits { get; set; } = new();

        /// <summary>
        /// 内容限制
        /// </summary>
        public ContentLimits ContentLimits { get; set; } = new();

        /// <summary>
        /// 地理限制
        /// </summary>
        public GeographicLimits GeographicLimits { get; set; } = new();

        /// <summary>
        /// 技术限制
        /// </summary>
        public TechnicalLimits TechnicalLimits { get; set; } = new();

        /// <summary>
        /// 业务限制
        /// </summary>
        public BusinessLimits BusinessLimits { get; set; } = new();

        /// <summary>
        /// 自定义限制
        /// </summary>
        public Dictionary<string, object> CustomLimits { get; set; } = new();
    }

    /// <summary>
    /// 速率限制
    /// </summary>
    public class RateLimits
    {
        public int RequestsPerSecond { get; set; } = 10;
        public int RequestsPerMinute { get; set; } = 600;
        public int RequestsPerHour { get; set; } = 36000;
        public int RequestsPerDay { get; set; } = 864000;
        public int TokensPerMinute { get; set; } = 60000;
        public int TokensPerHour { get; set; } = 3600000;
        public int TokensPerDay { get; set; } = 86400000;
        public int ConcurrentRequests { get; set; } = 5;
        public TimeSpan BurstDuration { get; set; } = TimeSpan.FromSeconds(10);
        public int BurstLimit { get; set; } = 50;
        public bool EnableRateLimiting { get; set; } = true;
        public string RateLimitingStrategy { get; set; } = "TokenBucket";
    }

    /// <summary>
    /// 配额限制
    /// </summary>
    public class QuotaLimits
    {
        public long MonthlyTokenQuota { get; set; } = 1000000;
        public long DailyTokenQuota { get; set; } = 50000;
        public long MonthlyRequestQuota { get; set; } = 10000;
        public long DailyRequestQuota { get; set; } = 1000;
        public decimal MonthlySpendingLimit { get; set; } = 100.0m;
        public decimal DailySpendingLimit { get; set; } = 10.0m;
        public DateTime QuotaResetDate { get; set; } = DateTime.UtcNow.AddMonths(1);
        public bool EnableQuotaEnforcement { get; set; } = true;
        public bool EnableOverageCharges { get; set; } = false;
        public decimal OverageRate { get; set; } = 0.0m;
        public bool SendQuotaWarnings { get; set; } = true;
        public List<double> WarningThresholds { get; set; } = new() { 0.8, 0.9, 0.95 };
    }

    /// <summary>
    /// 大小限制
    /// </summary>
    public class SizeLimits
    {
        public long MaxRequestSize { get; set; } = 10 * 1024 * 1024; // 10MB
        public long MaxResponseSize { get; set; } = 50 * 1024 * 1024; // 50MB
        public int MaxInputTokens { get; set; } = 4096;
        public int MaxOutputTokens { get; set; } = 2048;
        public int MaxContextLength { get; set; } = 8192;
        public long MaxFileSize { get; set; } = 25 * 1024 * 1024; // 25MB
        public int MaxFilesPerRequest { get; set; } = 10;
        public long MaxImageSize { get; set; } = 20 * 1024 * 1024; // 20MB
        public int MaxImageWidth { get; set; } = 2048;
        public int MaxImageHeight { get; set; } = 2048;
        public TimeSpan MaxAudioDuration { get; set; } = TimeSpan.FromMinutes(25);
        public long MaxAudioSize { get; set; } = 25 * 1024 * 1024; // 25MB
    }

    /// <summary>
    /// 并发限制
    /// </summary>
    public class ConcurrencyLimits
    {
        public int MaxConcurrentRequests { get; set; } = 10;
        public int MaxConcurrentConnections { get; set; } = 100;
        public int MaxConcurrentStreams { get; set; } = 5;
        public int MaxQueueSize { get; set; } = 1000;
        public TimeSpan MaxQueueWaitTime { get; set; } = TimeSpan.FromMinutes(5);
        public bool EnableQueueing { get; set; } = true;
        public string QueueStrategy { get; set; } = "FIFO";
        public bool EnablePriorityQueuing { get; set; } = false;
        public int MaxPriorityLevels { get; set; } = 3;
    }

    /// <summary>
    /// 时间限制
    /// </summary>
    public class TimeLimits
    {
        public TimeSpan RequestTimeout { get; set; } = TimeSpan.FromSeconds(60);
        public TimeSpan ConnectionTimeout { get; set; } = TimeSpan.FromSeconds(30);
        public TimeSpan ReadTimeout { get; set; } = TimeSpan.FromSeconds(60);
        public TimeSpan WriteTimeout { get; set; } = TimeSpan.FromSeconds(60);
        public TimeSpan StreamingTimeout { get; set; } = TimeSpan.FromMinutes(10);
        public TimeSpan SessionTimeout { get; set; } = TimeSpan.FromHours(1);
        public TimeSpan IdleTimeout { get; set; } = TimeSpan.FromMinutes(15);
        public TimeSpan MaxProcessingTime { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan TokenExpiryTime { get; set; } = TimeSpan.FromHours(24);
    }

    /// <summary>
    /// 内容限制
    /// </summary>
    public class ContentLimits
    {
        public bool EnableContentFiltering { get; set; } = true;
        public List<string> ProhibitedContent { get; set; } = new();
        public List<string> AllowedContentTypes { get; set; } = new();
        public List<string> BlockedKeywords { get; set; } = new();
        public bool EnableProfanityFilter { get; set; } = true;
        public bool EnableAdultContentFilter { get; set; } = true;
        public bool EnableViolenceFilter { get; set; } = true;
        public bool EnableHateSpeechFilter { get; set; } = true;
        public string ContentModerationLevel { get; set; } = "Medium";
        public bool LogFilteredContent { get; set; } = true;
        public bool EnableCustomFilters { get; set; } = false;
        public List<string> CustomFilterRules { get; set; } = new();
    }

    /// <summary>
    /// 地理限制
    /// </summary>
    public class GeographicLimits
    {
        public List<string> AllowedCountries { get; set; } = new();
        public List<string> BlockedCountries { get; set; } = new();
        public List<string> AllowedRegions { get; set; } = new();
        public List<string> BlockedRegions { get; set; } = new();
        public bool EnableGeoBlocking { get; set; } = false;
        public bool EnableGeoRouting { get; set; } = false;
        public string DefaultRegion { get; set; } = string.Empty;
        public bool RequireRegionCompliance { get; set; } = false;
        public Dictionary<string, object> RegionSpecificLimits { get; set; } = new();
    }

    /// <summary>
    /// 技术限制
    /// </summary>
    public class TechnicalLimits
    {
        public List<string> SupportedProtocols { get; set; } = new() { "HTTPS" };
        public List<string> SupportedEncodings { get; set; } = new() { "UTF-8" };
        public List<string> SupportedCompressions { get; set; } = new() { "gzip", "deflate" };
        public string MinApiVersion { get; set; } = "1.0";
        public string MaxApiVersion { get; set; } = "2.0";
        public bool RequireHttps { get; set; } = true;
        public bool RequireAuthentication { get; set; } = true;
        public List<string> RequiredHeaders { get; set; } = new();
        public Dictionary<string, string> HeaderLimits { get; set; } = new();
        public bool EnableCors { get; set; } = true;
        public List<string> AllowedOrigins { get; set; } = new();
    }

    /// <summary>
    /// 业务限制
    /// </summary>
    public class BusinessLimits
    {
        public string ServiceTier { get; set; } = "Standard";
        public List<string> AvailableFeatures { get; set; } = new();
        public List<string> RestrictedFeatures { get; set; } = new();
        public bool RequireSubscription { get; set; } = false;
        public bool RequirePayment { get; set; } = false;
        public DateTime ServiceStartDate { get; set; } = DateTime.UtcNow;
        public DateTime? ServiceEndDate { get; set; }
        public bool EnableTrialPeriod { get; set; } = false;
        public TimeSpan TrialDuration { get; set; } = TimeSpan.FromDays(7);
        public Dictionary<string, object> TierSpecificLimits { get; set; } = new();
        public bool EnableUpgrade { get; set; } = true;
        public List<string> UpgradeOptions { get; set; } = new();
    }
}
