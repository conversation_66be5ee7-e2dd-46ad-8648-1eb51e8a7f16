using System;

namespace AITalk.Core.Enums
{
    /// <summary>
    /// 对话类型枚举
    /// 定义不同类型的AI对话模式，用于确定对话的处理方式和策略
    /// </summary>
    public enum ConversationType
    {
        /// <summary>
        /// 单轮对话 - 一次性问答，不保持上下文
        /// </summary>
        SingleRound,

        /// <summary>
        /// 多轮对话 - 保持上下文的连续对话
        /// </summary>
        MultiRound,

        /// <summary>
        /// 任务导向 - 专注于完成特定任务的对话
        /// </summary>
        TaskOriented,

        /// <summary>
        /// 闲聊模式 - 轻松的日常对话
        /// </summary>
        Casual,

        /// <summary>
        /// 流式传输 - 实时流式输出的对话
        /// </summary>
        Streaming,

        /// <summary>
        /// 批处理 - 批量处理多个对话请求
        /// </summary>
        Batch
    }

    /// <summary>
    /// 对话优先级枚举
    /// 定义对话的处理优先级，影响任务调度和资源分配
    /// </summary>
    public enum ConversationPriority
    {
        /// <summary>
        /// 低优先级 - 非紧急任务，可以延后处理
        /// </summary>
        Low,

        /// <summary>
        /// 普通优先级 - 常规任务，按正常顺序处理
        /// </summary>
        Normal,

        /// <summary>
        /// 高优先级 - 重要任务，优先处理
        /// </summary>
        High,

        /// <summary>
        /// 紧急优先级 - 紧急任务，需要快速处理
        /// </summary>
        Urgent,

        /// <summary>
        /// 关键优先级 - 最重要的任务，立即处理
        /// </summary>
        Critical
    }

    /// <summary>
    /// 对话状态枚举
    /// 定义对话在整个生命周期中的各种状态
    /// </summary>
    public enum ConversationStatus
    {
        /// <summary>
        /// 已创建 - 对话刚刚创建，尚未开始处理
        /// </summary>
        Created,

        /// <summary>
        /// 进行中 - 对话正在处理中，AI正在生成回复
        /// </summary>
        InProgress,

        /// <summary>
        /// 已暂停 - 对话被暂时暂停，可以恢复
        /// </summary>
        Paused,

        /// <summary>
        /// 已完成 - 对话成功完成，AI已生成完整回复
        /// </summary>
        Completed,

        /// <summary>
        /// 失败 - 对话处理失败，可能由于网络错误或API错误
        /// </summary>
        Failed,

        /// <summary>
        /// 已取消 - 对话被用户或系统主动取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 超时 - 对话处理超时，未能在规定时间内完成
        /// </summary>
        Timeout,

        /// <summary>
        /// 已归档 - 对话已完成并归档存储
        /// </summary>
        Archived
    }

    /// <summary>
    /// 消息角色枚举
    /// 定义对话中不同参与者的角色类型
    /// </summary>
    public enum MessageRole
    {
        /// <summary>
        /// 用户 - 发起对话的用户
        /// </summary>
        User,

        /// <summary>
        /// 助手 - AI助手，负责回复用户
        /// </summary>
        Assistant,

        /// <summary>
        /// 系统 - 系统级别的指令或提示
        /// </summary>
        System,

        /// <summary>
        /// 函数 - 函数调用的结果
        /// </summary>
        Function,

        /// <summary>
        /// 工具 - 工具调用的结果
        /// </summary>
        Tool
    }

    /// <summary>
    /// 消息内容类型枚举
    /// 定义消息可以包含的不同类型的内容格式
    /// </summary>
    public enum MessageContentType
    {
        /// <summary>
        /// 纯文本 - 普通的文字内容
        /// </summary>
        Text,

        /// <summary>
        /// 图像 - 图片文件或图像数据
        /// </summary>
        Image,

        /// <summary>
        /// 音频 - 音频文件或语音数据
        /// </summary>
        Audio,

        /// <summary>
        /// 视频 - 视频文件或视频数据
        /// </summary>
        Video,

        /// <summary>
        /// 文件 - 通用文件附件
        /// </summary>
        File,

        /// <summary>
        /// JSON - 结构化的JSON数据
        /// </summary>
        Json,

        /// <summary>
        /// Markdown - Markdown格式的文本
        /// </summary>
        Markdown,

        /// <summary>
        /// HTML - HTML格式的内容
        /// </summary>
        Html
    }

    /// <summary>
    /// 任务类型枚举
    /// 定义AI对话系统中可以执行的各种任务类型
    /// </summary>
    public enum TaskType
    {
        /// <summary>
        /// 初始分析 - 对用户输入进行初步分析
        /// </summary>
        InitialAnalysis,

        /// <summary>
        /// 信息收集 - 收集相关信息和数据
        /// </summary>
        InformationGathering,

        /// <summary>
        /// 上下文构建 - 构建对话上下文
        /// </summary>
        ContextBuilding,

        /// <summary>
        /// 主要处理 - 执行主要的处理逻辑
        /// </summary>
        PrimaryProcessing,

        /// <summary>
        /// 次要处理 - 执行辅助的处理逻辑
        /// </summary>
        SecondaryProcessing,

        /// <summary>
        /// 结果验证 - 验证处理结果的正确性
        /// </summary>
        ResultValidation,

        /// <summary>
        /// 结果优化 - 优化和改进处理结果
        /// </summary>
        ResultRefinement,

        /// <summary>
        /// 质量检查 - 检查输出质量
        /// </summary>
        QualityCheck,

        /// <summary>
        /// 用户确认 - 等待用户确认或反馈
        /// </summary>
        UserConfirmation,

        /// <summary>
        /// 最终合成 - 合成最终的回复内容
        /// </summary>
        FinalSynthesis,

        /// <summary>
        /// 错误处理 - 处理出现的错误
        /// </summary>
        ErrorHandling,

        /// <summary>
        /// 重试 - 重新执行失败的任务
        /// </summary>
        Retry,

        /// <summary>
        /// 回滚 - 回滚到之前的状态
        /// </summary>
        Rollback,

        /// <summary>
        /// 清理 - 清理临时数据和资源
        /// </summary>
        Cleanup,

        /// <summary>
        /// 聊天完成 - 标准的聊天对话完成
        /// </summary>
        ChatCompletion,

        /// <summary>
        /// 函数调用 - 调用外部函数或API
        /// </summary>
        FunctionCall,

        /// <summary>
        /// 数据检索 - 从数据源检索信息
        /// </summary>
        DataRetrieval,

        /// <summary>
        /// 分析 - 对数据进行分析处理
        /// </summary>
        Analysis,

        /// <summary>
        /// 自定义 - 用户自定义的任务类型
        /// </summary>
        Custom
    }

    /// <summary>
    /// 任务状态枚举
    /// 定义单个任务在执行过程中的状态
    /// </summary>
    public enum TaskStatus
    {
        /// <summary>
        /// 等待中 - 任务已创建，等待执行
        /// </summary>
        Pending,

        /// <summary>
        /// 运行中 - 任务正在执行
        /// </summary>
        Running,

        /// <summary>
        /// 已完成 - 任务成功完成
        /// </summary>
        Completed,

        /// <summary>
        /// 失败 - 任务执行失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已取消 - 任务被取消
        /// </summary>
        Cancelled,

        /// <summary>
        /// 超时 - 任务执行超时
        /// </summary>
        Timeout,

        /// <summary>
        /// 已跳过 - 任务被跳过执行
        /// </summary>
        Skipped,

        /// <summary>
        /// 重试中 - 任务正在重试
        /// </summary>
        Retrying
    }

    /// <summary>
    /// 任务执行状态枚举
    /// 定义任务执行器的详细状态信息
    /// </summary>
    public enum TaskExecutionStatus
    {
        /// <summary>
        /// 未开始 - 任务尚未开始执行
        /// </summary>
        NotStarted,

        /// <summary>
        /// 初始化中 - 任务正在初始化
        /// </summary>
        Initializing,

        /// <summary>
        /// 运行中 - 任务正在运行
        /// </summary>
        Running,

        /// <summary>
        /// 等待中 - 任务等待外部条件
        /// </summary>
        Waiting,

        /// <summary>
        /// 已暂停 - 任务被暂停
        /// </summary>
        Suspended,

        /// <summary>
        /// 已完成 - 任务执行完成
        /// </summary>
        Completed,

        /// <summary>
        /// 失败 - 任务执行失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已取消 - 任务被取消
        /// </summary>
        Cancelled
    }

    /// <summary>
    /// 循环检测结果枚举
    /// 定义系统检测到的循环类型，用于防止无限循环
    /// </summary>
    public enum LoopDetectionResult
    {
        /// <summary>
        /// 无循环 - 未检测到循环
        /// </summary>
        NoLoop,

        /// <summary>
        /// 潜在循环 - 可能存在循环
        /// </summary>
        PotentialLoop,

        /// <summary>
        /// 确认循环 - 确认存在循环
        /// </summary>
        ConfirmedLoop,

        /// <summary>
        /// 无限循环 - 检测到无限循环
        /// </summary>
        InfiniteLoop
    }

    /// <summary>
    /// 循环中断策略枚举
    /// 定义当检测到循环时采用的中断策略
    /// </summary>
    public enum LoopBreakingStrategy
    {
        /// <summary>
        /// 修改任务参数 - 通过修改参数打破循环
        /// </summary>
        ModifyTaskParameters,

        /// <summary>
        /// 改变执行顺序 - 通过改变执行顺序打破循环
        /// </summary>
        ChangeExecutionOrder,

        /// <summary>
        /// 引入随机性 - 通过引入随机因素打破循环
        /// </summary>
        IntroduceRandomness,

        /// <summary>
        /// 请求用户干预 - 请求用户手动干预
        /// </summary>
        RequestUserIntervention,

        /// <summary>
        /// 使用替代服务 - 切换到备用服务
        /// </summary>
        UseAlternativeService,

        /// <summary>
        /// 简化任务 - 简化任务复杂度
        /// </summary>
        SimplifyTask,

        /// <summary>
        /// 跳过可选步骤 - 跳过非必要的步骤
        /// </summary>
        SkipOptionalSteps,

        /// <summary>
        /// 强制终止 - 强制终止循环
        /// </summary>
        ForceTermination
    }

    /// <summary>
    /// 下一步动作枚举
    /// 定义任务完成后系统应该采取的下一步行动
    /// </summary>
    public enum NextAction
    {
        /// <summary>
        /// 继续 - 继续执行下一个任务
        /// </summary>
        Continue,

        /// <summary>
        /// 暂停 - 暂停执行，等待条件满足
        /// </summary>
        Pause,

        /// <summary>
        /// 完成 - 标记整个流程完成
        /// </summary>
        Complete,

        /// <summary>
        /// 重试 - 重新执行当前任务
        /// </summary>
        Retry,

        /// <summary>
        /// 升级 - 将问题升级到更高级别处理
        /// </summary>
        Escalate,

        /// <summary>
        /// 请求输入 - 请求用户提供更多输入
        /// </summary>
        RequestInput,

        /// <summary>
        /// 终止 - 终止整个流程
        /// </summary>
        Terminate
    }

    /// <summary>
    /// 结果冲突类型枚举
    /// 定义多个任务结果之间可能出现的冲突类型
    /// </summary>
    public enum ResultConflictType
    {
        /// <summary>
        /// 矛盾 - 结果之间存在直接矛盾
        /// </summary>
        Contradiction,

        /// <summary>
        /// 不一致 - 结果之间不一致
        /// </summary>
        Inconsistency,

        /// <summary>
        /// 模糊 - 结果含义模糊不清
        /// </summary>
        Ambiguity,

        /// <summary>
        /// 不完整 - 结果信息不完整
        /// </summary>
        Incompleteness,

        /// <summary>
        /// 冗余 - 结果存在重复信息
        /// </summary>
        Redundancy
    }

    /// <summary>
    /// 冲突解决策略枚举
    /// 定义当出现结果冲突时采用的解决策略
    /// </summary>
    public enum ConflictResolutionStrategy
    {
        /// <summary>
        /// 使用最新 - 采用最新的结果
        /// </summary>
        UseLatest,

        /// <summary>
        /// 使用最高置信度 - 采用置信度最高的结果
        /// </summary>
        UseHighestConfidence,

        /// <summary>
        /// 合并 - 尝试合并多个结果
        /// </summary>
        Merge,

        /// <summary>
        /// 请求澄清 - 请求用户澄清
        /// </summary>
        RequestClarification,

        /// <summary>
        /// 使用默认 - 使用系统默认值
        /// </summary>
        UseDefault,

        /// <summary>
        /// 手动处理 - 需要手动干预处理
        /// </summary>
        Manual
    }


}
