using System;
using System.Collections.Generic;
using AITalk.Core.Enums;

namespace AITalk.Core.Models.OpenAI
{
    /// <summary>
    /// OpenAI兼容提供商
    /// </summary>
    public class OpenAICompatibleProvider
    {
        /// <summary>
        /// 提供商ID
        /// </summary>
        public Guid ProviderId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 提供商名称
        /// </summary>
        public string ProviderName { get; set; } = string.Empty;

        /// <summary>
        /// 提供商类型
        /// </summary>
        public ProviderType ProviderType { get; set; }

        /// <summary>
        /// 基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// API版本
        /// </summary>
        public string ApiVersion { get; set; } = "v1";

        /// <summary>
        /// 兼容性级别
        /// </summary>
        public CompatibilityLevel CompatibilityLevel { get; set; } = CompatibilityLevel.High;

        /// <summary>
        /// 支持的端点
        /// </summary>
        public List<SupportedEndpoint> SupportedEndpoints { get; set; } = new();

        /// <summary>
        /// 支持的模型
        /// </summary>
        public List<ProviderModel> SupportedModels { get; set; } = new();

        /// <summary>
        /// 认证方式
        /// </summary>
        public AuthMethod AuthenticationMethod { get; set; } = AuthMethod.BearerToken;

        /// <summary>
        /// 特殊请求头
        /// </summary>
        public Dictionary<string, string> SpecialHeaders { get; set; } = new();

        /// <summary>
        /// 请求转换规则
        /// </summary>
        public List<RequestTransformRule> RequestTransformRules { get; set; } = new();

        /// <summary>
        /// 响应转换规则
        /// </summary>
        public List<ResponseTransformRule> ResponseTransformRules { get; set; } = new();

        /// <summary>
        /// 速率限制配置
        /// </summary>
        public RateLimitConfiguration RateLimits { get; set; } = new();

        /// <summary>
        /// 定价信息
        /// </summary>
        public PricingConfiguration PricingInfo { get; set; } = new();

        /// <summary>
        /// 功能支持矩阵
        /// </summary>
        public FeatureSupportMatrix FeatureSupport { get; set; } = new();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 配置元数据
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// 支持的端点
    /// </summary>
    public class SupportedEndpoint
    {
        public EndpointType Type { get; set; }
        public string Path { get; set; } = string.Empty;
        public List<string> SupportedMethods { get; set; } = new();
        public bool IsAvailable { get; set; } = true;
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 提供商模型
    /// </summary>
    public class ProviderModel
    {
        public string ModelId { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public ModelType ModelType { get; set; }
        public int ContextLength { get; set; }
        public int MaxOutputTokens { get; set; }
        public bool SupportsStreaming { get; set; } = false;
        public bool SupportsFunctionCalling { get; set; } = false;
        public bool SupportsVision { get; set; } = false;
        public decimal InputPricePerToken { get; set; }
        public decimal OutputPricePerToken { get; set; }
        public bool IsAvailable { get; set; } = true;
        public DateTime? DeprecationDate { get; set; }
        public Dictionary<string, object> Capabilities { get; set; } = new();
    }

    /// <summary>
    /// 请求转换规则
    /// </summary>
    public class RequestTransformRule
    {
        public string RuleName { get; set; } = string.Empty;
        public string SourceField { get; set; } = string.Empty;
        public string TargetField { get; set; } = string.Empty;
        public string TransformationType { get; set; } = string.Empty; // "rename", "map", "convert", "remove"
        public Dictionary<string, object> Parameters { get; set; } = new();
        public bool IsRequired { get; set; } = false;
    }

    /// <summary>
    /// 响应转换规则
    /// </summary>
    public class ResponseTransformRule
    {
        public string RuleName { get; set; } = string.Empty;
        public string SourceField { get; set; } = string.Empty;
        public string TargetField { get; set; } = string.Empty;
        public string TransformationType { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new();
        public bool IsRequired { get; set; } = false;
    }

    /// <summary>
    /// 速率限制配置
    /// </summary>
    public class RateLimitConfiguration
    {
        public int RequestsPerMinute { get; set; } = 60;
        public int RequestsPerHour { get; set; } = 3600;
        public int RequestsPerDay { get; set; } = 86400;
        public int TokensPerMinute { get; set; } = 60000;
        public int TokensPerHour { get; set; } = 3600000;
        public int TokensPerDay { get; set; } = 86400000;
        public int ConcurrentRequests { get; set; } = 10;
        public TimeSpan BurstDuration { get; set; } = TimeSpan.FromSeconds(10);
        public int BurstLimit { get; set; } = 100;
        public bool EnableRateLimiting { get; set; } = true;
        public string RateLimitingStrategy { get; set; } = "TokenBucket";
    }

    /// <summary>
    /// 定价配置
    /// </summary>
    public class PricingConfiguration
    {
        public string PricingModel { get; set; } = "PayPerToken";
        public decimal DefaultInputTokenPrice { get; set; }
        public decimal DefaultOutputTokenPrice { get; set; }
        public Dictionary<string, ModelPricing> ModelSpecificPricing { get; set; } = new();
        public string Currency { get; set; } = "USD";
        public bool HasFreeTier { get; set; } = false;
        public long FreeTierTokenLimit { get; set; } = 0;
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 模型定价
    /// </summary>
    public class ModelPricing
    {
        public string ModelId { get; set; } = string.Empty;
        public decimal InputTokenPrice { get; set; }
        public decimal OutputTokenPrice { get; set; }
        public decimal RequestPrice { get; set; }
        public Dictionary<string, decimal> TierPricing { get; set; } = new();
    }

    /// <summary>
    /// 功能支持矩阵
    /// </summary>
    public class FeatureSupportMatrix
    {
        public bool SupportsStreaming { get; set; } = false;
        public bool SupportsFunctionCalling { get; set; } = false;
        public bool SupportsVision { get; set; } = false;
        public bool SupportsAudio { get; set; } = false;
        public bool SupportsImageGeneration { get; set; } = false;
        public bool SupportsEmbeddings { get; set; } = false;
        public bool SupportsModeration { get; set; } = false;
        public bool SupportsFineTuning { get; set; } = false;
        public bool SupportsBatchProcessing { get; set; } = false;
        public bool SupportsCustomModels { get; set; } = false;
        public List<string> SupportedLanguages { get; set; } = new();
        public List<string> SupportedFormats { get; set; } = new();
        public Dictionary<string, bool> CustomFeatures { get; set; } = new();
    }

    /// <summary>
    /// API连接
    /// </summary>
    public class ApiConnection
    {
        public Guid ConnectionId { get; set; } = Guid.NewGuid();
        public ProviderType ProviderType { get; set; }
        public string BaseUrl { get; set; } = string.Empty;
        public DateTime EstablishedAt { get; set; } = DateTime.UtcNow;
        public DateTime LastUsedAt { get; set; } = DateTime.UtcNow;
        public bool IsActive { get; set; } = true;
        public int RequestCount { get; set; } = 0;
        public TimeSpan AverageResponseTime { get; set; }
        public Dictionary<string, object> ConnectionMetadata { get; set; } = new();
    }

    /// <summary>
    /// 流式响应块
    /// </summary>
    public class StreamingChunk
    {
        public string Id { get; set; } = string.Empty;
        public string Object { get; set; } = "chat.completion.chunk";
        public long Created { get; set; }
        public string Model { get; set; } = string.Empty;
        public List<StreamingChoice> Choices { get; set; } = new();
        public bool IsComplete { get; set; } = false;
        public string? FinishReason { get; set; }
        public DateTime ReceivedAt { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    /// <summary>
    /// 流式选择
    /// </summary>
    public class StreamingChoice
    {
        public int Index { get; set; }
        public StreamingDelta? Delta { get; set; }
        public string? FinishReason { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    /// <summary>
    /// 流式增量
    /// </summary>
    public class StreamingDelta
    {
        public string? Role { get; set; }
        public string? Content { get; set; }
        public object? FunctionCall { get; set; }
        public Dictionary<string, object> AdditionalData { get; set; } = new();
    }

    /// <summary>
    /// 模型信息
    /// </summary>
    public class ModelInfo
    {
        public string ModelId { get; set; } = string.Empty;
        public string ModelName { get; set; } = string.Empty;
        public ProviderType Provider { get; set; }
        public ModelType ModelType { get; set; }
        public int ContextLength { get; set; }
        public int MaxOutputTokens { get; set; }
        public bool SupportsFunctionCalling { get; set; } = false;
        public bool SupportsVision { get; set; } = false;
        public bool SupportsAudio { get; set; } = false;
        public bool SupportsStreaming { get; set; } = false;
        public decimal InputPricePerToken { get; set; }
        public decimal OutputPricePerToken { get; set; }
        public string Description { get; set; } = string.Empty;
        public DateTime ReleaseDate { get; set; }
        public DateTime? DeprecationDate { get; set; }
        public List<string> RecommendedUseCase { get; set; } = new();
        public Dictionary<string, object> Capabilities { get; set; } = new();
        public bool IsAvailable { get; set; } = true;
    }

    /// <summary>
    /// 兼容性报告
    /// </summary>
    public class CompatibilityReport
    {
        public ProviderType ProviderType { get; set; }
        public CompatibilityLevel CompatibilityLevel { get; set; }
        public double CompatibilityScore { get; set; }
        public List<string> SupportedFeatures { get; set; } = new();
        public List<string> UnsupportedFeatures { get; set; } = new();
        public List<string> PartiallySupported { get; set; } = new();
        public List<CompatibilityIssue> Issues { get; set; } = new();
        public List<Workaround> Workarounds { get; set; } = new();
        public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 兼容性问题
    /// </summary>
    public class CompatibilityIssue
    {
        public string IssueType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Severity { get; set; } = string.Empty; // "low", "medium", "high", "critical"
        public string Impact { get; set; } = string.Empty;
        public List<string> AffectedFeatures { get; set; } = new();
    }

    /// <summary>
    /// 解决方案
    /// </summary>
    public class Workaround
    {
        public string WorkaroundType { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Implementation { get; set; } = string.Empty;
        public List<string> ApplicableFeatures { get; set; } = new();
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    /// <summary>
    /// 连接测试结果
    /// </summary>
    public class ConnectionTestResult
    {
        public bool IsSuccessful { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public Dictionary<string, string> ResponseHeaders { get; set; } = new();
        public DateTime TestedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// 提供商基准测试
    /// </summary>
    public class ProviderBenchmark
    {
        public ProviderType ProviderType { get; set; }
        public TimeSpan AverageResponseTime { get; set; }
        public double ThroughputPerSecond { get; set; }
        public double SuccessRate { get; set; }
        public double ErrorRate { get; set; }
        public Dictionary<string, TimeSpan> EndpointResponseTimes { get; set; } = new();
        public Dictionary<string, double> FeaturePerformance { get; set; } = new();
        public DateTime BenchmarkedAt { get; set; } = DateTime.UtcNow;
        public TimeSpan BenchmarkDuration { get; set; }
        public int TotalRequests { get; set; }
    }

    /// <summary>
    /// 速率限制状态
    /// </summary>
    public class RateLimitStatus
    {
        public bool CanProceed { get; set; } = true;
        public int RemainingRequests { get; set; }
        public int RemainingTokens { get; set; }
        public DateTime ResetTime { get; set; }
        public TimeSpan RetryAfter { get; set; }
        public string LimitType { get; set; } = string.Empty; // "requests", "tokens", "concurrent"
        public Dictionary<string, object> Details { get; set; } = new();
    }

    /// <summary>
    /// OpenAI错误
    /// </summary>
    public class OpenAIError
    {
        public string ErrorCode { get; set; } = string.Empty;
        public string ErrorMessage { get; set; } = string.Empty;
        public string ErrorType { get; set; } = string.Empty;
        public object? ErrorDetails { get; set; }
        public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
        public ProviderType ProviderType { get; set; }
        public Dictionary<string, object> Context { get; set; } = new();
    }

    /// <summary>
    /// 速率限制异常
    /// </summary>
    public class RateLimitExceededException : Exception
    {
        public RateLimitExceededException(string message) : base(message) { }
        public RateLimitExceededException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// OpenAI API异常
    /// </summary>
    public class OpenAIApiException : Exception
    {
        public string ErrorCode { get; }
        
        public OpenAIApiException(string message, string errorCode) : base(message)
        {
            ErrorCode = errorCode;
        }
        
        public OpenAIApiException(string message, string errorCode, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }
}
