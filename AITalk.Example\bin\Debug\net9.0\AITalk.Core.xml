<?xml version="1.0"?>
<doc>
    <assembly>
        <name>AITalk.Core</name>
    </assembly>
    <members>
        <member name="T:AITalk.Core.Enums.ConversationType">
            <summary>
            对话类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ConversationPriority">
            <summary>
            对话优先级枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ConversationStatus">
            <summary>
            对话状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.MessageRole">
            <summary>
            消息角色枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.MessageContentType">
            <summary>
            消息内容类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.TaskType">
            <summary>
            任务类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.TaskStatus">
            <summary>
            任务状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.TaskExecutionStatus">
            <summary>
            任务执行状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.LoopDetectionResult">
            <summary>
            循环检测结果枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.LoopBreakingStrategy">
            <summary>
            循环中断策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.NextAction">
            <summary>
            下一步动作枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ResultConflictType">
            <summary>
            结果冲突类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ConflictResolutionStrategy">
            <summary>
            冲突解决策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ProviderType">
            <summary>
            OpenAI兼容提供商类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.CompatibilityLevel">
            <summary>
            兼容性级别枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.AuthMethod">
            <summary>
            认证方法枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ModelType">
            <summary>
            模型类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.StandardErrorCode">
            <summary>
            标准错误代码枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.RetryStrategyType">
            <summary>
            重试策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.RateLimitAction">
            <summary>
            速率限制动作枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.AuthRecoveryAction">
            <summary>
            认证恢复动作枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.QuotaRecoveryAction">
            <summary>
            配额恢复动作枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.LocalLLMType">
            <summary>
            本地LLM类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ProviderHealthStatus">
            <summary>
            提供商健康状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.StreamingStatus">
            <summary>
            流式响应状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.EndpointType">
            <summary>
            端点类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ExportFormat">
            <summary>
            导出格式枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.CacheStrategy">
            <summary>
            缓存策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ServiceType">
            <summary>
            服务器类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.AuthenticationType">
            <summary>
            认证类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ProtocolType">
            <summary>
            协议类型枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.ServiceStatus">
            <summary>
            服务状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.LoadBalancingStrategy">
            <summary>
            负载均衡策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.CompressionLevel">
            <summary>
            压缩级别枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.LogLevel">
            <summary>
            日志级别枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.CircuitBreakerState">
            <summary>
            熔断器状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.RetryStrategy">
            <summary>
            重试策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.QueueOverflowStrategy">
            <summary>
            队列溢出策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.TaskSchedulingStrategy">
            <summary>
            任务调度策略枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Enums.TaskExecutionOrder">
            <summary>
            任务执行顺序枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AITalkData">
            <summary>
            AI对话数据 - 存储对话相关的所有数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Id">
            <summary>
            数据唯一标识
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.ConversationInfo">
            <summary>
            对话基础信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.MessageHistory">
            <summary>
            消息历史
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.TaskQueue">
            <summary>
            任务队列状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Context">
            <summary>
            上下文信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Statistics">
            <summary>
            对话统计信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Configuration">
            <summary>
            对话配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.State">
            <summary>
            对话状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Result">
            <summary>
            对话结果
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.Version">
            <summary>
            版本号
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.IsDeleted">
            <summary>
            是否已删除（软删除）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkData.ExtendedProperties">
            <summary>
            扩展属性
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.AddMessage(AITalk.Core.Models.ConversationData.ConversationMessage)">
            <summary>
            添加消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.AddTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            添加任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.GetNextTask">
            <summary>
            获取下一个待执行任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.StartTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            开始执行任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.CompleteTask(AITalk.Core.Models.ConversationData.AITask,System.Object)">
            <summary>
            完成任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.FailTask(AITalk.Core.Models.ConversationData.AITask,System.Exception)">
            <summary>
            任务失败
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.CancelTask(AITalk.Core.Models.ConversationData.AITask,System.String)">
            <summary>
            取消任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.UpdateStatus(AITalk.Core.Enums.ConversationStatus,System.String)">
            <summary>
            更新对话状态
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.UpdateStatistics">
            <summary>
            更新统计信息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.GetCurrentRound">
            <summary>
            获取当前轮次
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.IsCompleted">
            <summary>
            检查对话是否完成
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.IsActive">
            <summary>
            检查对话是否活跃
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.GetSummary">
            <summary>
            获取对话摘要
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.CleanupExpiredData(System.TimeSpan)">
            <summary>
            清理过期数据
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.ExportToJson">
            <summary>
            导出对话数据
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkData.ValidateIntegrity">
            <summary>
            验证数据完整性
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AITalkServiceData">
            <summary>
            AI对话服务数据 - 存储服务器相关的详细信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Id">
            <summary>
            服务数据唯一标识
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.ServerInfo">
            <summary>
            服务器基础信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Authentication">
            <summary>
            认证信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Performance">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Usage">
            <summary>
            使用统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Configuration">
            <summary>
            配置设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Health">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Capabilities">
            <summary>
            服务能力信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Limits">
            <summary>
            服务限制信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Pricing">
            <summary>
            定价信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.SLA">
            <summary>
            服务等级协议
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Monitoring">
            <summary>
            监控配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Backup">
            <summary>
            备份和恢复配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.ExtendedProperties">
            <summary>
            扩展属性
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.CreatedBy">
            <summary>
            创建者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.LastUpdatedBy">
            <summary>
            最后更新者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Version">
            <summary>
            版本号
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.IsDeleted">
            <summary>
            是否已删除（软删除）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.DeletedAt">
            <summary>
            删除时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.DeletedBy">
            <summary>
            删除者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.AITalkServiceData.Notes">
            <summary>
            备注
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.UpdateHealth(System.Boolean,System.String)">
            <summary>
            更新健康状态
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.UpdatePerformanceMetrics(System.TimeSpan,System.Boolean)">
            <summary>
            更新性能指标
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.UpdateUsageStatistics(System.Int64,System.Decimal)">
            <summary>
            更新使用统计
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.IsAvailable">
            <summary>
            检查是否可用
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.GetEffectiveWeight">
            <summary>
            获取服务权重（用于负载均衡）
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.AITalkServiceData.Clone">
            <summary>
            克隆服务数据（深拷贝）
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ApiRequest">
            <summary>
            API请求基类
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ApiResponse`1">
            <summary>
            API响应基类
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ChatCompletionRequest">
            <summary>
            聊天完成请求
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ChatMessage">
            <summary>
            聊天消息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.Function">
            <summary>
            函数定义
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ChatCompletionResponse">
            <summary>
            聊天完成响应
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.Choice">
            <summary>
            选择项
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.Usage">
            <summary>
            使用情况
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.StreamingRequest">
            <summary>
            流式请求
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.StreamingResponse">
            <summary>
            流式响应
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ValidationResult">
            <summary>
            验证结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.HealthCheckResult">
            <summary>
            健康检查结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.PerformanceReport">
            <summary>
            性能报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.PerformanceIssue">
            <summary>
            性能问题
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.PerformanceRecommendation">
            <summary>
            性能建议
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AuthenticationResult">
            <summary>
            认证结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TokenRefreshResult">
            <summary>
            令牌刷新结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.EncryptedCredentials">
            <summary>
            加密凭据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.KeyRotationResult">
            <summary>
            密钥轮换结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.SecurityEvent">
            <summary>
            安全事件
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AnomalyAlert">
            <summary>
            异常告警
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.CertificateValidationResult">
            <summary>
            证书验证结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.SecurityPolicy">
            <summary>
            安全策略
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.AITask">
            <summary>
            AI任务 - 多轮调度的核心单元
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.TaskId">
            <summary>
            任务ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.TaskType">
            <summary>
            任务类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.TaskName">
            <summary>
            任务名称
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Description">
            <summary>
            任务描述
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.StartedAt">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.CompletedAt">
            <summary>
            完成时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.EstimatedDuration">
            <summary>
            预估持续时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ActualDuration">
            <summary>
            实际持续时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Progress">
            <summary>
            进度百分比 (0-100)
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Input">
            <summary>
            输入数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Output">
            <summary>
            输出数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Error">
            <summary>
            错误信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.MaxRetries">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Dependencies">
            <summary>
            依赖任务ID列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Dependents">
            <summary>
            依赖此任务的任务ID列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ParentTaskId">
            <summary>
            父任务ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ChildTaskIds">
            <summary>
            子任务ID列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Round">
            <summary>
            所属轮次
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ExecutionOrder">
            <summary>
            执行顺序
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.IsRootTask">
            <summary>
            是否为根任务
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.IsLeafTask">
            <summary>
            是否为叶子任务
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.CanExecuteInParallel">
            <summary>
            是否可并行执行
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.RequiresUserInput">
            <summary>
            是否需要用户输入
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.RequiresExternalService">
            <summary>
            是否需要外部服务
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ExpectedOutputType">
            <summary>
            期望输出类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ValidationRules">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.TransformationRules">
            <summary>
            转换规则
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ContinuationCondition">
            <summary>
            继续条件
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.TerminationCondition">
            <summary>
            终止条件
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Tags">
            <summary>
            任务标签
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Metadata">
            <summary>
            任务元数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Configuration">
            <summary>
            任务配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.ExecutionContext">
            <summary>
            任务执行上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Result">
            <summary>
            任务结果
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.AITask.Metrics">
            <summary>
            任务指标
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Start">
            <summary>
            开始任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Complete(System.Object)">
            <summary>
            完成任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Fail(System.Exception)">
            <summary>
            任务失败
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Cancel(System.String)">
            <summary>
            取消任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.UpdateProgress(System.Double,System.String)">
            <summary>
            更新进度
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.CanRetry">
            <summary>
            重试任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Retry">
            <summary>
            执行重试
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.AreDependenciesSatisfied(System.Collections.Generic.Dictionary{System.Guid,AITalk.Core.Models.ConversationData.AITask})">
            <summary>
            检查依赖是否满足
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.GetDuration">
            <summary>
            获取任务持续时间
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.IsTimedOut">
            <summary>
            检查是否超时
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.AITask.Clone">
            <summary>
            克隆任务
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationInfo">
            <summary>
            对话基础信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ConversationId">
            <summary>
            对话唯一标识
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.SessionId">
            <summary>
            会话ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Title">
            <summary>
            对话标题
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Description">
            <summary>
            对话描述
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ConversationType">
            <summary>
            对话类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Status">
            <summary>
            状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.StartedAt">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.CompletedAt">
            <summary>
            完成时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.LastActivityAt">
            <summary>
            最后活动时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ExpiresAt">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Tags">
            <summary>
            标签
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Metadata">
            <summary>
            元数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ParentConversationId">
            <summary>
            父对话ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ChildConversationIds">
            <summary>
            子对话ID列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.IsArchived">
            <summary>
            是否归档
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ArchiveReason">
            <summary>
            归档原因
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.ArchivedAt">
            <summary>
            归档时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.CreatedBy">
            <summary>
            创建者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.LastUpdatedBy">
            <summary>
            最后更新者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Version">
            <summary>
            版本号
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.IsDeleted">
            <summary>
            是否已删除（软删除）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.DeletedAt">
            <summary>
            删除时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.DeletedBy">
            <summary>
            删除者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Configuration">
            <summary>
            对话配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Statistics">
            <summary>
            对话统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationInfo.Quality">
            <summary>
            对话质量评估
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationInfo.UpdateStatus(AITalk.Core.Enums.ConversationStatus,System.String)">
            <summary>
            更新状态
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationInfo.GetDuration">
            <summary>
            计算对话持续时间
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationInfo.IsExpired">
            <summary>
            检查是否过期
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationInfo.IsActive">
            <summary>
            检查是否活跃
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.StatusChange">
            <summary>
            状态变化记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationConfiguration">
            <summary>
            对话配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationStatistics">
            <summary>
            对话统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationQuality">
            <summary>
            对话质量评估
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationMessage">
            <summary>
            对话消息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.MessageId">
            <summary>
            消息ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Role">
            <summary>
            消息角色
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Content">
            <summary>
            消息内容
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ContentType">
            <summary>
            内容类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Timestamp">
            <summary>
            时间戳
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.TokenCount">
            <summary>
            Token数量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ProcessingTime">
            <summary>
            处理时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ServiceId">
            <summary>
            使用的服务ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ModelName">
            <summary>
            使用的模型名称
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Temperature">
            <summary>
            温度参数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.MaxTokens">
            <summary>
            最大Token数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Attachments">
            <summary>
            附件列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Metadata">
            <summary>
            消息元数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.IsEdited">
            <summary>
            是否已编辑
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.EditHistory">
            <summary>
            编辑历史
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Reactions">
            <summary>
            消息反应
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.References">
            <summary>
            消息引用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ParentMessageId">
            <summary>
            父消息ID（用于回复）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ChildMessageIds">
            <summary>
            子消息ID列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Priority">
            <summary>
            消息优先级
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Status">
            <summary>
            消息状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.ErrorMessage">
            <summary>
            错误信息（如果有）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.RetryCount">
            <summary>
            重试次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.IsDeleted">
            <summary>
            是否已删除（软删除）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.DeletedAt">
            <summary>
            删除时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Quality">
            <summary>
            消息质量评分
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ConversationMessage.Security">
            <summary>
            消息安全信息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.EditContent(System.String,System.String)">
            <summary>
            编辑消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.AddReaction(System.String,System.String)">
            <summary>
            添加反应
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.RemoveReaction(System.String,System.String)">
            <summary>
            移除反应
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.AddReference(System.Guid,System.String)">
            <summary>
            添加引用
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.AddAttachment(AITalk.Core.Models.ConversationData.MessageAttachment)">
            <summary>
            添加附件
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.GetContentLength">
            <summary>
            计算内容长度
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.GetSummary(System.Int32)">
            <summary>
            获取消息摘要
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.ContainsSensitiveContent">
            <summary>
            检查是否包含敏感内容
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.ConversationMessage.Clone">
            <summary>
            克隆消息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageStatus">
            <summary>
            消息状态枚举
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageAttachment">
            <summary>
            消息附件
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageEdit">
            <summary>
            消息编辑记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageReaction">
            <summary>
            消息反应
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageReference">
            <summary>
            消息引用
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageQuality">
            <summary>
            消息质量
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageSecurity">
            <summary>
            消息安全
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ContextInformation">
            <summary>
            上下文信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.SessionContext">
            <summary>
            会话上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.UserContext">
            <summary>
            用户上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.SystemContext">
            <summary>
            系统上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.ConversationContext">
            <summary>
            对话上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.TemporalContext">
            <summary>
            时间上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.EnvironmentContext">
            <summary>
            环境上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.CustomContext">
            <summary>
            自定义上下文
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.ContextVersion">
            <summary>
            上下文版本
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.ContextChecksum">
            <summary>
            上下文校验和
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.LastUpdated">
            <summary>
            最后更新时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.ExpiresAt">
            <summary>
            过期时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.IsReadOnly">
            <summary>
            是否只读
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.CompressionLevel">
            <summary>
            压缩级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.EncryptionLevel">
            <summary>
            加密级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.ContextInformation.TaskOutputs">
            <summary>
            任务输出缓存
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.SessionContextData">
            <summary>
            会话上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.UserContextData">
            <summary>
            用户上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.SystemContextData">
            <summary>
            系统上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationContextData">
            <summary>
            对话上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TemporalContextData">
            <summary>
            时间上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.EnvironmentContextData">
            <summary>
            环境上下文数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationState">
            <summary>
            对话状态
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.StateTransition">
            <summary>
            状态转换记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationResult">
            <summary>
            对话结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ConversationSummary">
            <summary>
            对话摘要
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageHistory">
            <summary>
            消息历史
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.Messages">
            <summary>
            消息列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.TotalMessageCount">
            <summary>
            总消息数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.UserMessageCount">
            <summary>
            用户消息数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.AssistantMessageCount">
            <summary>
            助手消息数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.SystemMessageCount">
            <summary>
            系统消息数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.MaxHistoryLength">
            <summary>
            最大历史长度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.CompressionEnabled">
            <summary>
            是否启用压缩
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.CompressionRatio">
            <summary>
            压缩比率
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.EncryptionEnabled">
            <summary>
            是否启用加密
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.BackupEnabled">
            <summary>
            是否启用备份
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.LastBackupAt">
            <summary>
            最后备份时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.MessageHistory.Statistics">
            <summary>
            历史统计信息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.AddMessage(AITalk.Core.Models.ConversationData.ConversationMessage)">
            <summary>
            添加消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.GetRecentMessages(System.Int32)">
            <summary>
            获取最近的消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.GetMessagesByRole(AITalk.Core.Enums.MessageRole)">
            <summary>
            获取指定角色的消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.GetMessagesByTimeRange(System.DateTime,System.DateTime)">
            <summary>
            获取指定时间范围的消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.SearchMessages(System.String,System.Boolean)">
            <summary>
            搜索消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.GetMessageStatistics">
            <summary>
            获取消息统计
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.CleanupOldMessages(System.TimeSpan)">
            <summary>
            清理旧消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.CompressHistory">
            <summary>
            压缩历史记录
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.UpdateStatistics">
            <summary>
            更新统计信息
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.ExportToJson">
            <summary>
            导出消息历史
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.MessageHistory.ImportFromJson(System.String)">
            <summary>
            从JSON导入消息历史
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageHistoryStatistics">
            <summary>
            消息历史统计信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.MessageStatistics">
            <summary>
            消息统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.ValidationRule">
            <summary>
            验证规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TransformationRule">
            <summary>
            转换规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TaskConfiguration">
            <summary>
            任务配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TaskExecutionContext">
            <summary>
            任务执行上下文
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TaskResult">
            <summary>
            任务结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TaskMetrics">
            <summary>
            任务指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.TaskQueueState">
            <summary>
            任务队列状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.PendingTasks">
            <summary>
            待处理任务队列
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.ExecutingTasks">
            <summary>
            执行中任务字典
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.CompletedTasks">
            <summary>
            已完成任务列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.FailedTasks">
            <summary>
            失败任务列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.CancelledTasks">
            <summary>
            取消任务列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.MaxConcurrentTasks">
            <summary>
            最大并发任务数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.CurrentConcurrentTasks">
            <summary>
            当前并发任务数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.TaskExecutionOrder">
            <summary>
            任务执行顺序
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.TaskRetryPolicy">
            <summary>
            任务重试策略
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.TaskTimeout">
            <summary>
            任务超时时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.QueueCapacity">
            <summary>
            队列容量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.QueueOverflowStrategy">
            <summary>
            队列溢出策略
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.TaskSchedulingStrategy">
            <summary>
            任务调度策略
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.BatchSize">
            <summary>
            批处理大小
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.BatchInterval">
            <summary>
            批处理间隔
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ConversationData.TaskQueueState.Statistics">
            <summary>
            队列统计
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.EnqueueTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            添加任务到队列
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.DequeueTask">
            <summary>
            从队列获取下一个任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.StartExecutingTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            开始执行任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.CompleteTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            完成任务执行
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.FailTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            任务执行失败
            </summary>
        </member>
        <member name="M:AITalk.Core.Models.ConversationData.TaskQueueState.CancelTask(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            取消任务
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.RetryPolicy">
            <summary>
            重试策略
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.RetryStrategy">
            <summary>
            重试策略枚举
            </summary>
        </member>
        <member name="F:AITalk.Core.Models.ConversationData.RetryStrategy.Fixed">
            <summary>
            固定延迟
            </summary>
        </member>
        <member name="F:AITalk.Core.Models.ConversationData.RetryStrategy.Exponential">
            <summary>
            指数退避
            </summary>
        </member>
        <member name="F:AITalk.Core.Models.ConversationData.RetryStrategy.Linear">
            <summary>
            线性退避
            </summary>
        </member>
        <member name="F:AITalk.Core.Models.ConversationData.RetryStrategy.Jitter">
            <summary>
            随机抖动
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationData.QueueStatistics">
            <summary>
            队列统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider">
            <summary>
            OpenAI兼容提供商
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.ProviderId">
            <summary>
            提供商ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.ProviderName">
            <summary>
            提供商名称
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.ProviderType">
            <summary>
            提供商类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.BaseUrl">
            <summary>
            基础URL
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.ApiVersion">
            <summary>
            API版本
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.CompatibilityLevel">
            <summary>
            兼容性级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.SupportedEndpoints">
            <summary>
            支持的端点
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.SupportedModels">
            <summary>
            支持的模型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.AuthenticationMethod">
            <summary>
            认证方式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.SpecialHeaders">
            <summary>
            特殊请求头
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.RequestTransformRules">
            <summary>
            请求转换规则
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.ResponseTransformRules">
            <summary>
            响应转换规则
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.RateLimits">
            <summary>
            速率限制配置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.PricingInfo">
            <summary>
            定价信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.FeatureSupport">
            <summary>
            功能支持矩阵
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.IsEnabled">
            <summary>
            是否启用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.OpenAI.OpenAICompatibleProvider.Metadata">
            <summary>
            配置元数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.SupportedEndpoint">
            <summary>
            支持的端点
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ProviderModel">
            <summary>
            提供商模型
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RequestTransformRule">
            <summary>
            请求转换规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ResponseTransformRule">
            <summary>
            响应转换规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitConfiguration">
            <summary>
            速率限制配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.PricingConfiguration">
            <summary>
            定价配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelPricing">
            <summary>
            模型定价
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.FeatureSupportMatrix">
            <summary>
            功能支持矩阵
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ApiConnection">
            <summary>
            API连接
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.StreamingChunk">
            <summary>
            流式响应块
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.StreamingChoice">
            <summary>
            流式选择
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.StreamingDelta">
            <summary>
            流式增量
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelInfo">
            <summary>
            模型信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.CompatibilityReport">
            <summary>
            兼容性报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.CompatibilityIssue">
            <summary>
            兼容性问题
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.Workaround">
            <summary>
            解决方案
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ConnectionTestResult">
            <summary>
            连接测试结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ProviderBenchmark">
            <summary>
            提供商基准测试
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitStatus">
            <summary>
            速率限制状态
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.OpenAIError">
            <summary>
            OpenAI错误
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitExceededException">
            <summary>
            速率限制异常
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.OpenAIApiException">
            <summary>
            OpenAI API异常
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.CompletionResponse">
            <summary>
            完成响应
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.CompletionChoice">
            <summary>
            完成选择
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.EmbeddingResponse">
            <summary>
            嵌入响应
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.EmbeddingData">
            <summary>
            嵌入数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelCapabilities">
            <summary>
            模型能力
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelLimits">
            <summary>
            模型限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelMatch">
            <summary>
            模型匹配
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelRequirements">
            <summary>
            模型需求
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelPerformance">
            <summary>
            模型性能
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ModelRecommendation">
            <summary>
            模型推荐
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.EndpointDocumentation">
            <summary>
            端点文档
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.EndpointParameter">
            <summary>
            端点参数
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.EndpointExample">
            <summary>
            端点示例
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.StreamingError">
            <summary>
            流式错误
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RetryInstruction">
            <summary>
            重试指令
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitError">
            <summary>
            速率限制错误
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.AuthError">
            <summary>
            认证错误
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.QuotaError">
            <summary>
            配额错误
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ErrorReport">
            <summary>
            错误报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.QuotaInfo">
            <summary>
            配额信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitHeaders">
            <summary>
            速率限制头信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.RateLimitStatistics">
            <summary>
            速率限制统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.ProviderAdapterBase">
            <summary>
            提供商适配器基类
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.OpenAIProvider">
            <summary>
            OpenAI提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.AzureOpenAIProvider">
            <summary>
            Azure OpenAI提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.AnthropicProvider">
            <summary>
            Anthropic提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.GoogleProvider">
            <summary>
            Google提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.OllamaProvider">
            <summary>
            Ollama提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.OpenAI.GenericOpenAIProvider">
            <summary>
            通用OpenAI提供商
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PricingInfo">
            <summary>
            定价信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ServiceLevelAgreement">
            <summary>
            服务等级协议
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.MonitoringConfiguration">
            <summary>
            监控配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.AlertRule">
            <summary>
            告警规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.BackupConfiguration">
            <summary>
            备份配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.AuthenticationInfo">
            <summary>
            认证信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.AuthType">
            <summary>
            认证类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.ApiKey">
            <summary>
            API密钥（加密存储）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.SecretKey">
            <summary>
            密钥（加密存储）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.AccessToken">
            <summary>
            访问令牌
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.RefreshToken">
            <summary>
            刷新令牌
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.TokenExpiry">
            <summary>
            令牌过期时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.OrganizationId">
            <summary>
            组织ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.ProjectId">
            <summary>
            项目ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.UserId">
            <summary>
            用户ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.Scopes">
            <summary>
            权限范围
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.CustomHeaders">
            <summary>
            自定义请求头
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.CertificatePath">
            <summary>
            证书路径
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.EncryptionKey">
            <summary>
            加密密钥
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.Username">
            <summary>
            用户名（用于Basic认证）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.Password">
            <summary>
            密码（用于Basic认证）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.ClientId">
            <summary>
            OAuth2客户端ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.ClientSecret">
            <summary>
            OAuth2客户端密钥
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.AuthorizationUrl">
            <summary>
            OAuth2授权URL
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.TokenUrl">
            <summary>
            OAuth2令牌URL
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.RedirectUri">
            <summary>
            OAuth2重定向URI
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.JwtSigningKey">
            <summary>
            JWT签名密钥
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.JwtIssuer">
            <summary>
            JWT发行者
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.JwtAudience">
            <summary>
            JWT受众
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.TwoFactorEnabled">
            <summary>
            是否启用双因素认证
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.TwoFactorSecret">
            <summary>
            双因素认证密钥
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.CreatedAt">
            <summary>
            认证创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.UpdatedAt">
            <summary>
            认证更新时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.LastValidatedAt">
            <summary>
            最后验证时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.IsValid">
            <summary>
            认证是否有效
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.FailureCount">
            <summary>
            认证失败次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.MaxFailures">
            <summary>
            最大失败次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.AuthenticationInfo.LockoutExpiry">
            <summary>
            锁定到期时间
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ConfigurationSettings">
            <summary>
            配置设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.ConnectionTimeout">
            <summary>
            连接超时
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.ReadTimeout">
            <summary>
            读取超时
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.WriteTimeout">
            <summary>
            写入超时
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.MaxRetryAttempts">
            <summary>
            最大重试次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.RetryDelay">
            <summary>
            重试延迟
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.RetryBackoffMultiplier">
            <summary>
            重试退避倍数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CircuitBreakerThreshold">
            <summary>
            熔断器阈值
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CircuitBreakerTimeout">
            <summary>
            熔断器超时
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.HealthCheckInterval">
            <summary>
            健康检查间隔
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.KeepAliveInterval">
            <summary>
            保活间隔
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.MaxConnectionPoolSize">
            <summary>
            最大连接池大小
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.ConnectionPoolTimeout">
            <summary>
            连接池超时
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.EnableCompression">
            <summary>
            启用压缩
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CompressionLevel">
            <summary>
            压缩级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.EnableCaching">
            <summary>
            启用缓存
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CacheExpiry">
            <summary>
            缓存过期时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.LogLevel">
            <summary>
            日志级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.EnableMetrics">
            <summary>
            启用指标收集
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CustomSettings">
            <summary>
            自定义设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.DefaultHeaders">
            <summary>
            请求头设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.UserAgent">
            <summary>
            用户代理
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.MaxRequestSize">
            <summary>
            最大请求大小（字节）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.MaxResponseSize">
            <summary>
            最大响应大小（字节）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.EnableSslVerification">
            <summary>
            启用SSL验证
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.SslProtocol">
            <summary>
            SSL协议版本
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.ProxySettings">
            <summary>
            代理设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.LoadBalancing">
            <summary>
            负载均衡设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.RetryPolicy">
            <summary>
            重试策略设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.CircuitBreaker">
            <summary>
            熔断器设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.Cache">
            <summary>
            缓存设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.Monitoring">
            <summary>
            监控设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ConfigurationSettings.Security">
            <summary>
            安全设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ProxySettings">
            <summary>
            代理设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.LoadBalancingSettings">
            <summary>
            负载均衡设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.RetryPolicySettings">
            <summary>
            重试策略设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.CircuitBreakerSettings">
            <summary>
            熔断器设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.CacheSettings">
            <summary>
            缓存设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.MonitoringSettings">
            <summary>
            监控设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.SecuritySettings">
            <summary>
            安全设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.HealthStatus">
            <summary>
            健康状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.IsOnline">
            <summary>
            是否在线
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.IsHealthy">
            <summary>
            是否健康
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.LastHealthCheck">
            <summary>
            最后健康检查时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.LastSuccessfulRequest">
            <summary>
            最后成功请求时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.LastFailedRequest">
            <summary>
            最后失败请求时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.ConsecutiveFailures">
            <summary>
            连续失败次数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.HealthScore">
            <summary>
            健康评分 (0-100)
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.StatusMessage">
            <summary>
            状态消息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.ErrorLogs">
            <summary>
            错误日志
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.WarningLogs">
            <summary>
            警告日志
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.MaintenanceMode">
            <summary>
            维护模式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.MaintenanceStartTime">
            <summary>
            维护开始时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.MaintenanceEndTime">
            <summary>
            维护结束时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.ExpectedDowntime">
            <summary>
            预期停机时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.AlertThresholds">
            <summary>
            告警阈值设置
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.ServiceStatus">
            <summary>
            服务状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.DetailedStatus">
            <summary>
            详细状态信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.DependencyStatus">
            <summary>
            依赖服务状态
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.Performance">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.Availability">
            <summary>
            可用性统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.HealthCheckHistory">
            <summary>
            健康检查历史
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.HealthStatus.AutoRecovery">
            <summary>
            自动恢复设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ErrorLogEntry">
            <summary>
            错误日志条目
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.WarningLogEntry">
            <summary>
            警告日志条目
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.AlertThresholdSettings">
            <summary>
            告警阈值设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.HealthPerformanceMetrics">
            <summary>
            健康性能指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.AvailabilityStatistics">
            <summary>
            可用性统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.DowntimeIncident">
            <summary>
            停机事件
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.HealthCheckRecord">
            <summary>
            健康检查记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.AutoRecoverySettings">
            <summary>
            自动恢复设置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PerformanceMetrics">
            <summary>
            性能指标
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.AverageResponseTime">
            <summary>
            平均响应时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MinResponseTime">
            <summary>
            最小响应时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MaxResponseTime">
            <summary>
            最大响应时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.P95ResponseTime">
            <summary>
            95分位响应时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.P99ResponseTime">
            <summary>
            99分位响应时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ThroughputPerSecond">
            <summary>
            每秒吞吐量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ThroughputPerMinute">
            <summary>
            每分钟吞吐量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ThroughputPerHour">
            <summary>
            每小时吞吐量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.SuccessRate">
            <summary>
            成功率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ErrorRate">
            <summary>
            错误率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.TimeoutRate">
            <summary>
            超时率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.RetryRate">
            <summary>
            重试率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.AvailabilityPercentage">
            <summary>
            可用性百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.LastMeasuredAt">
            <summary>
            最后测量时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MeasurementWindow">
            <summary>
            测量窗口
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ConcurrentConnections">
            <summary>
            并发连接数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MaxConcurrentConnections">
            <summary>
            最大并发连接数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.QueueLength">
            <summary>
            队列长度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MaxQueueLength">
            <summary>
            最大队列长度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.CpuUsagePercentage">
            <summary>
            CPU使用率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.MemoryUsagePercentage">
            <summary>
            内存使用率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.NetworkUsagePercentage">
            <summary>
            网络带宽使用率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.DiskUsagePercentage">
            <summary>
            磁盘使用率百分比
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ResponseTimeHistory">
            <summary>
            响应时间历史记录
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.ErrorHistory">
            <summary>
            错误历史记录
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.TrendData">
            <summary>
            性能趋势数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.Benchmark">
            <summary>
            性能基准数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.PerformanceMetrics.Thresholds">
            <summary>
            性能告警阈值
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ResponseTimeRecord">
            <summary>
            响应时间记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ErrorRecord">
            <summary>
            错误记录
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PerformanceSnapshot">
            <summary>
            性能快照
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PerformanceBenchmark">
            <summary>
            性能基准
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PerformanceThresholds">
            <summary>
            性能阈值
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ServerInfo">
            <summary>
            服务器基础信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.ServerId">
            <summary>
            服务器唯一标识
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.ServerName">
            <summary>
            服务器名称
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.ServerType">
            <summary>
            服务器类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.BaseUrl">
            <summary>
            基础URL
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.IPAddress">
            <summary>
            IP地址
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Port">
            <summary>
            端口号
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Protocol">
            <summary>
            协议类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Region">
            <summary>
            地理区域
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.DataCenter">
            <summary>
            数据中心
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Version">
            <summary>
            API版本
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.SupportedModels">
            <summary>
            支持的模型列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.MaxConcurrentRequests">
            <summary>
            最大并发请求数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.IsActive">
            <summary>
            是否激活
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.CreatedAt">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.UpdatedAt">
            <summary>
            更新时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Description">
            <summary>
            服务器描述
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Tags">
            <summary>
            服务器标签
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Metadata">
            <summary>
            自定义元数据
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Weight">
            <summary>
            服务器权重（用于负载均衡）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.Priority">
            <summary>
            优先级
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.IsBackup">
            <summary>
            是否为备用服务器
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.PrimaryServerId">
            <summary>
            主服务器ID（如果这是备用服务器）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServerInfo.BackupServerIds">
            <summary>
            备用服务器ID列表
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ServiceCapabilities">
            <summary>
            服务能力信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedFeatures">
            <summary>
            支持的功能列表
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedModelTypes">
            <summary>
            支持的模型类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedEndpoints">
            <summary>
            支持的端点类型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsStreaming">
            <summary>
            支持流式响应
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsFunctionCalling">
            <summary>
            支持函数调用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsVision">
            <summary>
            支持视觉输入
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsAudio">
            <summary>
            支持音频输入
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsImageGeneration">
            <summary>
            支持图像生成
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsDocumentProcessing">
            <summary>
            支持文档处理
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsCodeGeneration">
            <summary>
            支持代码生成
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedLanguages">
            <summary>
            支持多语言
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedInputFormats">
            <summary>
            支持的输入格式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedOutputFormats">
            <summary>
            支持的输出格式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.MaxContextLength">
            <summary>
            最大上下文长度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.MaxOutputLength">
            <summary>
            最大输出长度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.TemperatureRange">
            <summary>
            支持的温度范围
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.TopPRange">
            <summary>
            支持的Top-P范围
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsBatchProcessing">
            <summary>
            支持批处理
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.MaxBatchSize">
            <summary>
            最大批处理大小
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsAsyncProcessing">
            <summary>
            支持异步处理
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsResponseCaching">
            <summary>
            支持缓存
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsCompression">
            <summary>
            支持压缩
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedCompressionFormats">
            <summary>
            支持的压缩格式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsWebhooks">
            <summary>
            支持Webhook
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsRealTimeComm">
            <summary>
            支持实时通信
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedAuthMethods">
            <summary>
            支持的认证方式
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsCustomModels">
            <summary>
            支持自定义模型
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsFineTuning">
            <summary>
            支持微调
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsEmbeddings">
            <summary>
            支持嵌入向量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.EmbeddingDimensions">
            <summary>
            嵌入向量维度
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportsModeration">
            <summary>
            支持内容审核
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SupportedSecurityLevels">
            <summary>
            支持的安全级别
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.GeographicRestrictions">
            <summary>
            地理限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.DataRetentionPolicy">
            <summary>
            数据保留政策
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.PrivacyCompliance">
            <summary>
            隐私合规性
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.ApiVersionCompatibility">
            <summary>
            API版本兼容性
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.SpecialFeatures">
            <summary>
            特殊功能
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.Performance">
            <summary>
            性能特征
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.Reliability">
            <summary>
            可靠性特征
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceCapabilities.Scalability">
            <summary>
            扩展性特征
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.PerformanceCharacteristics">
            <summary>
            性能特征
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ReliabilityCharacteristics">
            <summary>
            可靠性特征
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ScalabilityCharacteristics">
            <summary>
            扩展性特征
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ServiceLimits">
            <summary>
            服务限制信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.RateLimits">
            <summary>
            请求速率限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.QuotaLimits">
            <summary>
            配额限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.SizeLimits">
            <summary>
            大小限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.ConcurrencyLimits">
            <summary>
            并发限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.TimeLimits">
            <summary>
            时间限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.ContentLimits">
            <summary>
            内容限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.GeographicLimits">
            <summary>
            地理限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.TechnicalLimits">
            <summary>
            技术限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.BusinessLimits">
            <summary>
            业务限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.ServiceLimits.CustomLimits">
            <summary>
            自定义限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.RateLimits">
            <summary>
            速率限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.QuotaLimits">
            <summary>
            配额限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.SizeLimits">
            <summary>
            大小限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ConcurrencyLimits">
            <summary>
            并发限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.TimeLimits">
            <summary>
            时间限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.ContentLimits">
            <summary>
            内容限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.GeographicLimits">
            <summary>
            地理限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.TechnicalLimits">
            <summary>
            技术限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.BusinessLimits">
            <summary>
            业务限制
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageStatistics">
            <summary>
            使用统计信息
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalRequests">
            <summary>
            总请求数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalSuccessfulRequests">
            <summary>
            成功请求数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalFailedRequests">
            <summary>
            失败请求数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalTokensUsed">
            <summary>
            总Token使用量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.InputTokensUsed">
            <summary>
            输入Token使用量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.OutputTokensUsed">
            <summary>
            输出Token使用量
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalCost">
            <summary>
            总费用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.CostPerToken">
            <summary>
            每Token费用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.CostPerRequest">
            <summary>
            每请求费用
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.DailyUsage">
            <summary>
            每日使用统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.MonthlyUsage">
            <summary>
            每月使用统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.HourlyUsage">
            <summary>
            每小时使用统计
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.QuotaLimit">
            <summary>
            配额限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.QuotaUsed">
            <summary>
            已使用配额
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.QuotaResetDate">
            <summary>
            配额重置日期
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.RateLimitPerMinute">
            <summary>
            每分钟速率限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.RateLimitPerHour">
            <summary>
            每小时速率限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.RateLimitPerDay">
            <summary>
            每日速率限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.AverageRequestSize">
            <summary>
            平均请求大小（字节）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.AverageResponseSize">
            <summary>
            平均响应大小（字节）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.TotalDataTransferred">
            <summary>
            总数据传输量（字节）
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.PeakConcurrentRequests">
            <summary>
            峰值并发请求数
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.PeakConcurrentTime">
            <summary>
            峰值并发时间
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.PatternAnalysis">
            <summary>
            使用模式分析
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.CostAnalysis">
            <summary>
            成本分析
            </summary>
        </member>
        <member name="P:AITalk.Core.Models.ServiceData.UsageStatistics.LastUpdated">
            <summary>
            统计更新时间
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageDayData">
            <summary>
            每日使用数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageMonthData">
            <summary>
            每月使用数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageHourData">
            <summary>
            每小时使用数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsagePatternAnalysis">
            <summary>
            使用模式分析
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.CostAnalysis">
            <summary>
            成本分析
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageSpike">
            <summary>
            使用峰值
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.UsageTrend">
            <summary>
            使用趋势
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.CostOptimizationSuggestion">
            <summary>
            成本优化建议
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceData.CostTrend">
            <summary>
            成本趋势
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceLoadInfo">
            <summary>
            服务负载信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.FailoverEvent">
            <summary>
            故障转移事件
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.RequestMetrics">
            <summary>
            请求指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ResponseMetrics">
            <summary>
            响应指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.RealTimeMetrics">
            <summary>
            实时指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AlertConfiguration">
            <summary>
            告警配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AlertRule">
            <summary>
            告警规则
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TrendData">
            <summary>
            趋势数据
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ComparisonReport">
            <summary>
            比较报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ComparisonInsight">
            <summary>
            比较洞察
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceConfiguration">
            <summary>
            服务配置
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConfigurationVersion">
            <summary>
            配置版本
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConfigurationChange">
            <summary>
            配置变更
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.LoopPattern">
            <summary>
            循环模式
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.LoopStatistics">
            <summary>
            循环统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.RoundResult">
            <summary>
            轮次结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.RoundMetrics">
            <summary>
            轮次指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConversationProgress">
            <summary>
            对话进度
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.IntermediateResult">
            <summary>
            中间结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TaskExecutionResult">
            <summary>
            任务执行结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TaskExecutionProgress">
            <summary>
            任务执行进度
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AggregatedResult">
            <summary>
            聚合结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ResultConflict">
            <summary>
            结果冲突
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ConflictResolutionResult">
            <summary>
            冲突解决结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ExecutionPlan">
            <summary>
            执行计划
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ExecutionPhase">
            <summary>
            执行阶段
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TaskFailureStrategy">
            <summary>
            任务失败策略
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.TaskCompletionResult">
            <summary>
            任务完成结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.AnalysisResult">
            <summary>
            分析结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.PartialResult">
            <summary>
            部分结果
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ResultSummary">
            <summary>
            结果摘要
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ResultGap">
            <summary>
            结果缺口
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ImprovementSuggestion">
            <summary>
            改进建议
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceStatistics">
            <summary>
            服务统计
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.ServiceAlert">
            <summary>
            服务告警
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.WorkflowState">
            <summary>
            工作流状态
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.DecisionPoint">
            <summary>
            决策点
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.DecisionOption">
            <summary>
            决策选项
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.DecisionCriteria">
            <summary>
            决策标准
            </summary>
        </member>
        <member name="T:AITalk.Core.Models.QualityMetrics">
            <summary>
            质量指标
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.AITalk">
            <summary>
            AI对话类 - 负责具体的对话内容和多轮调度
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.ConversationId">
            <summary>
            对话ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.TaskQueue">
            <summary>
            任务队列 - 多轮调度的核心
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.CurrentTask">
            <summary>
            当前执行任务
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.MaxRounds">
            <summary>
            最大轮次限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.CurrentRound">
            <summary>
            当前轮次
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.IsCompleted">
            <summary>
            是否完成
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.IsCancelled">
            <summary>
            是否取消
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.FinalResult">
            <summary>
            最终结果
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.AITalk.Data">
            <summary>
            关联的数据对象
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.StartConversationAsync(System.String)">
            <summary>
            开始对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.ProcessCurrentRoundAsync">
            <summary>
            处理当前轮次
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.ExecuteTaskAsync(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            执行单个任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.AddUserMessageAsync(System.String)">
            <summary>
            添加用户消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.AddAssistantMessage(System.String,System.Nullable{System.Guid},System.String)">
            <summary>
            添加助手消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.CancelConversationAsync(System.String)">
            <summary>
            取消对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.PauseConversationAsync">
            <summary>
            暂停对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.ResumeConversationAsync">
            <summary>
            恢复对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.GetProgress">
            <summary>
            获取对话进度
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.GetAllTasks">
            <summary>
            获取所有任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.GetTaskHistory">
            <summary>
            获取任务历史
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.GetExecutableTasksAsync">
            <summary>
            获取可执行的任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.HandleInfiniteLoopAsync">
            <summary>
            处理无限循环
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.CalculateRoundMetrics(AITalk.Core.Models.RoundResult,System.TimeSpan)">
            <summary>
            计算轮次指标
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.ShouldContinueConversationAsync(AITalk.Core.Models.RoundResult)">
            <summary>
            判断是否应该继续对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalk.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.AITalkService">
            <summary>
            AI对话服务 - 主要管理第三方服务器的地址、方法、API Key等
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.RegisterServiceAsync(AITalk.Core.Models.AITalkServiceData)">
            <summary>
            注册服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.UnregisterServiceAsync(System.Guid)">
            <summary>
            注销服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.DiscoverServicesAsync">
            <summary>
            发现服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.GetServiceByIdAsync(System.Guid)">
            <summary>
            根据ID获取服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.GetServicesByTypeAsync(AITalk.Core.Enums.ServiceType)">
            <summary>
            根据类型获取服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.GetHealthyServicesAsync">
            <summary>
            获取健康的服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.SelectBestServiceAsync(System.Collections.Generic.List{AITalk.Core.Models.AITalkServiceData},AITalk.Core.Enums.LoadBalancingStrategy)">
            <summary>
            选择最佳服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.SelectServiceForTaskAsync(AITalk.Core.Enums.TaskType,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            选择服务用于特定任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.SendChatCompletionAsync(AITalk.Core.Models.AITalkServiceData,AITalk.Core.Models.ChatCompletionRequest)">
            <summary>
            发送聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.SendStreamingRequestAsync(AITalk.Core.Models.AITalkServiceData,AITalk.Core.Models.StreamingRequest)">
            <summary>
            发送流式请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.UpdateServiceConfigurationAsync(System.Guid,AITalk.Core.Models.ServiceData.ConfigurationSettings)">
            <summary>
            更新服务配置
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.GetServiceConfigurationAsync(System.Guid)">
            <summary>
            获取服务配置
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.PerformHealthCheckAsync(System.Guid)">
            <summary>
            执行健康检查
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.AITalkService.GetPerformanceReportAsync(System.Guid,System.TimeSpan)">
            <summary>
            获取服务性能报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IAITalk">
            <summary>
            AI对话接口
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.ConversationId">
            <summary>
            对话ID
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.TaskQueue">
            <summary>
            任务队列
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.CurrentTask">
            <summary>
            当前执行任务
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.MaxRounds">
            <summary>
            最大轮次限制
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.CurrentRound">
            <summary>
            当前轮次
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.IsCompleted">
            <summary>
            是否完成
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.IsCancelled">
            <summary>
            是否取消
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.FinalResult">
            <summary>
            最终结果
            </summary>
        </member>
        <member name="P:AITalk.Core.Services.Interfaces.IAITalk.Data">
            <summary>
            关联的数据对象
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.StartConversationAsync(System.String)">
            <summary>
            开始对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.ProcessCurrentRoundAsync">
            <summary>
            处理当前轮次
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.ExecuteTaskAsync(AITalk.Core.Models.ConversationData.AITask)">
            <summary>
            执行单个任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.AddUserMessageAsync(System.String)">
            <summary>
            添加用户消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.AddAssistantMessage(System.String,System.Nullable{System.Guid},System.String)">
            <summary>
            添加助手消息
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.CancelConversationAsync(System.String)">
            <summary>
            取消对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.PauseConversationAsync">
            <summary>
            暂停对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.ResumeConversationAsync">
            <summary>
            恢复对话
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalk.GetProgress">
            <summary>
            获取对话进度
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.ITaskSchedulingEngine">
            <summary>
            任务调度引擎接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.ITaskExecutor">
            <summary>
            任务执行器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IConversationFlowController">
            <summary>
            对话流程控制器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IResultAggregator">
            <summary>
            结果聚合器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.ISelfLoopDetector">
            <summary>
            自循环检测器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IAITalkServiceManage">
            <summary>
            AI对话服务管理接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IAITalkService">
            <summary>
            AI对话服务接口
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.RegisterServiceAsync(AITalk.Core.Models.AITalkServiceData)">
            <summary>
            注册服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.UnregisterServiceAsync(System.Guid)">
            <summary>
            注销服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.DiscoverServicesAsync">
            <summary>
            发现服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.GetServiceByIdAsync(System.Guid)">
            <summary>
            根据ID获取服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.GetServicesByTypeAsync(AITalk.Core.Enums.ServiceType)">
            <summary>
            根据类型获取服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.GetHealthyServicesAsync">
            <summary>
            获取健康的服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.SelectBestServiceAsync(System.Collections.Generic.List{AITalk.Core.Models.AITalkServiceData},AITalk.Core.Enums.LoadBalancingStrategy)">
            <summary>
            选择最佳服务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.SelectServiceForTaskAsync(AITalk.Core.Enums.TaskType,System.Collections.Generic.Dictionary{System.String,System.Object})">
            <summary>
            选择服务用于特定任务
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.SendChatCompletionAsync(AITalk.Core.Models.AITalkServiceData,AITalk.Core.Models.ChatCompletionRequest)">
            <summary>
            发送聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.SendStreamingRequestAsync(AITalk.Core.Models.AITalkServiceData,AITalk.Core.Models.StreamingRequest)">
            <summary>
            发送流式请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.UpdateServiceConfigurationAsync(System.Guid,AITalk.Core.Models.ServiceData.ConfigurationSettings)">
            <summary>
            更新服务配置
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.GetServiceConfigurationAsync(System.Guid)">
            <summary>
            获取服务配置
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.PerformHealthCheckAsync(System.Guid)">
            <summary>
            执行健康检查
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IAITalkService.GetPerformanceReportAsync(System.Guid,System.TimeSpan)">
            <summary>
            获取服务性能报告
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IServiceDiscovery">
            <summary>
            服务发现接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.ILoadBalancer">
            <summary>
            负载均衡器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IApiClient">
            <summary>
            API客户端接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IPerformanceMonitor">
            <summary>
            性能监控接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IConfigurationManager">
            <summary>
            配置管理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.ISecurityManager">
            <summary>
            安全管理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAIApiManager">
            <summary>
            OpenAI API管理器接口
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.RegisterProviderAsync(AITalk.Core.Models.OpenAI.OpenAICompatibleProvider)">
            <summary>
            注册OpenAI兼容提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.UnregisterProviderAsync(AITalk.Core.Enums.ProviderType)">
            <summary>
            注销提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.GetAvailableProvidersAsync">
            <summary>
            获取可用提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.SendChatCompletionAsync(AITalk.Core.Models.ChatCompletionRequest,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            发送聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.SendStreamingChatCompletionAsync(AITalk.Core.Models.ChatCompletionRequest,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            发送流式聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.GetAvailableModelsAsync(System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            获取可用模型
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.Interfaces.IOpenAIApiManager.GetModelInfoAsync(System.String,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            获取模型信息
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAICompatibleProvider">
            <summary>
            OpenAI兼容提供商接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAIRequestRouter">
            <summary>
            OpenAI请求路由器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAIResponseNormalizer">
            <summary>
            OpenAI响应标准化器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IProviderCompatibilityChecker">
            <summary>
            提供商兼容性检查器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IEndpointMapper">
            <summary>
            端点映射器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAIModelManager">
            <summary>
            OpenAI模型管理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IStreamingResponseHandler">
            <summary>
            流式响应处理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IOpenAIErrorHandler">
            <summary>
            OpenAI错误处理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.Interfaces.IRateLimitManager">
            <summary>
            速率限制管理器接口
            </summary>
        </member>
        <member name="T:AITalk.Core.Services.OpenAIApiManager">
            <summary>
            OpenAI格式API管理器 - 管理第三方OpenAI兼容API
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.RegisterProviderAsync(AITalk.Core.Models.OpenAI.OpenAICompatibleProvider)">
            <summary>
            注册OpenAI兼容提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.UnregisterProviderAsync(AITalk.Core.Enums.ProviderType)">
            <summary>
            注销提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.GetAvailableProvidersAsync">
            <summary>
            获取可用提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.SendChatCompletionAsync(AITalk.Core.Models.ChatCompletionRequest,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            发送聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.SendStreamingChatCompletionAsync(AITalk.Core.Models.ChatCompletionRequest,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            发送流式聊天完成请求
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.GetAvailableModelsAsync(System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            获取可用模型
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.GetModelInfoAsync(System.String,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            获取模型信息
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.SelectProviderAsync(AITalk.Core.Models.ChatCompletionRequest,System.Nullable{AITalk.Core.Enums.ProviderType})">
            <summary>
            选择最佳提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.SelectProviderByModelAsync(System.String)">
            <summary>
            根据模型选择提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.SelectBestAvailableProviderAsync">
            <summary>
            选择最佳可用提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.CalculateProviderScoreAsync(AITalk.Core.Services.Interfaces.IOpenAICompatibleProvider)">
            <summary>
            计算提供商评分
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.InitializeDefaultProviders">
            <summary>
            初始化默认提供商
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.CreateProviderInstance(AITalk.Core.Models.OpenAI.OpenAICompatibleProvider)">
            <summary>
            创建提供商实例
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.ValidateProviderConfigurationAsync(AITalk.Core.Models.OpenAI.OpenAICompatibleProvider)">
            <summary>
            验证提供商配置
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.LogErrorAsync(System.String,System.Exception)">
            <summary>
            记录错误
            </summary>
        </member>
        <member name="M:AITalk.Core.Services.OpenAIApiManager.Dispose">
            <summary>
            释放资源
            </summary>
        </member>
    </members>
</doc>
